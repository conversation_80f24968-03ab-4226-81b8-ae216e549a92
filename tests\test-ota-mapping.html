<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA渠道映射测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .channel-count {
            font-weight: bold;
            color: #007bff;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OTA渠道映射测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. OTA渠道映射加载测试</div>
            <div id="mappingStatus" class="status info">检查中...</div>
            <button class="test-button" onclick="testMappingLoad()">重新检查映射</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 批量OTA选择框测试</div>
            <select id="batchOtaSelect" class="batch-dropdown-btn">
                <option value="">选择OTA渠道</option>
                <!-- OTA选项将通过JavaScript动态生成 -->
            </select>
            <div id="selectStatus" class="status info">等待填充...</div>
            <button class="test-button" onclick="testSelectPopulation()">重新填充选项</button>
            <button class="test-button" onclick="showChannelDetails()">显示渠道详情</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 渠道映射功能测试</div>
            <input type="text" id="testChannel" placeholder="输入渠道名称测试映射" style="margin-bottom: 10px;">
            <button class="test-button" onclick="testChannelMapping()">测试映射</button>
            <div id="mappingResult" class="status info">输入渠道名称进行测试</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 所有可用渠道列表</div>
            <div id="channelList" class="info" style="max-height: 300px; overflow-y: auto;">
                正在加载渠道列表...
            </div>
        </div>
    </div>

    <!-- 引入必要的JS文件 -->
    <script src="js/logger.js"></script>
    <script src="js/ota-channel-config.js"></script>

    <script>
        // 测试脚本
        function testMappingLoad() {
            const statusDiv = document.getElementById('mappingStatus');
            
            try {
                if (window.OTA && window.OTA.otaChannelConfig) {
                    const config = window.OTA.otaChannelConfig;
                    const channels = config.channels || [];
                    const channelCount = channels.length;
                    
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        ✅ OTA渠道配置加载成功<br>
                        <span class="channel-count">渠道数量: ${channelCount}个</span><br>
                        配置对象类型: ${typeof config}<br>
                        channels类型: ${Array.isArray(channels) ? '数组' : typeof channels}
                    `;
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '❌ OTA渠道配置未加载<br>检查 ota-channel-config.js 是否正确加载';
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ 检查映射时出错: ${error.message}`;
            }
        }

        function testSelectPopulation() {
            const selectElement = document.getElementById('batchOtaSelect');
            const statusDiv = document.getElementById('selectStatus');
            
            try {
                // 使用直接填充方法（移除了旧的多订单管理器适配器依赖）
                // 直接填充方法
                populateOtaOptionsDirect(selectElement);
                const optionCount = selectElement.options.length - 1;
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `
                    ✅ OTA选项填充成功<br>
                    <span class="channel-count">填充选项数量: ${optionCount}个</span>
                `;
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ 填充选项失败: ${error.message}`;
            }
        }

        function populateOtaOptionsDirect(selectElement) {
            if (!selectElement) return;

            const otaChannelMapping = window.OTA?.otaChannelMapping;
            if (!otaChannelMapping) throw new Error('OTA渠道映射不可用');

            const commonChannels = otaChannelMapping.commonChannels || [];
            
            // 清空现有选项（保留第一个默认选项）
            while (selectElement.children.length > 1) {
                selectElement.removeChild(selectElement.lastChild);
            }

            // 添加所有通用渠道
            commonChannels.forEach(channel => {
                const option = document.createElement('option');
                option.value = channel.value;
                option.textContent = channel.text;
                selectElement.appendChild(option);
            });
        }

        function testChannelMapping() {
            const testInput = document.getElementById('testChannel');
            const resultDiv = document.getElementById('mappingResult');
            const channelName = testInput.value.trim();
            
            if (!channelName) {
                resultDiv.className = 'status error';
                resultDiv.innerHTML = '❌ 请输入要测试的渠道名称';
                return;
            }

            try {
                // 使用直接测试方法（移除了旧的多订单管理器适配器依赖）
                // 直接测试渠道显示逻辑
                const displayText = otaChannelMapping?.getDisplayName ? otaChannelMapping.getDisplayName(channelName) : channelName;
                
                resultDiv.className = 'status success';
                resultDiv.innerHTML = `
                    ✅ 渠道映射测试成功<br>
                    输入: <strong>${channelName}</strong><br>
                    映射结果: <strong>${displayText}</strong>
                `;
            } catch (error) {
                resultDiv.className = 'status error';
                resultDiv.innerHTML = `❌ 测试渠道映射失败: ${error.message}`;
            }
        }

        function showChannelDetails() {
            const channelListDiv = document.getElementById('channelList');
            
            try {
                const otaChannelMapping = window.OTA?.otaChannelMapping;
                if (!otaChannelMapping) {
                    channelListDiv.innerHTML = '❌ OTA渠道映射不可用';
                    return;
                }

                const commonChannels = otaChannelMapping.commonChannels || [];
                
                let html = `<strong>共 ${commonChannels.length} 个可用渠道:</strong><br><br>`;
                
                // 按分类显示
                const categories = {
                    '核心OTA平台': [],
                    'SMW相关渠道': [],
                    'GMH团队渠道': [],
                    'JR Coach Services': [],
                    '酒店合作伙伴': [],
                    'MapleHome系列': [],
                    '旅行社和代理商': [],
                    '其他渠道': []
                };

                commonChannels.forEach(channel => {
                    const name = channel.text.toLowerCase();
                    if (name.includes('klook') || name.includes('ctrip') || name.includes('fliggy') || name.includes('traveloka') || name.includes('heycar') || name.includes('mozio')) {
                        categories['核心OTA平台'].push(channel);
                    } else if (name.includes('smw')) {
                        categories['SMW相关渠道'].push(channel);
                    } else if (name.includes('gmh') || name.includes('gomyhire')) {
                        categories['GMH团队渠道'].push(channel);
                    } else if (name.includes('jr coach')) {
                        categories['JR Coach Services'].push(channel);
                    } else if (name.includes('hotel') || name.includes('boutique') || name.includes('méridien')) {
                        categories['酒店合作伙伴'].push(channel);
                    } else if (name.includes('maplehome') || name.includes('maple suite')) {
                        categories['MapleHome系列'].push(channel);
                    } else if (name.includes('travel') || name.includes('tour') || name.includes('dealer') || name.includes('b2b') || name.includes('ocean blue')) {
                        categories['旅行社和代理商'].push(channel);
                    } else {
                        categories['其他渠道'].push(channel);
                    }
                });

                Object.keys(categories).forEach(category => {
                    if (categories[category].length > 0) {
                        html += `<strong>${category} (${categories[category].length}个):</strong><br>`;
                        categories[category].forEach(channel => {
                            html += `• ${channel.text}<br>`;
                        });
                        html += '<br>';
                    }
                });

                channelListDiv.innerHTML = html;
            } catch (error) {
                channelListDiv.innerHTML = `❌ 显示渠道详情失败: ${error.message}`;
            }
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testMappingLoad();
                setTimeout(() => {
                    testSelectPopulation();
                    setTimeout(() => {
                        showChannelDetails();
                    }, 500);
                }, 500);
            }, 1000);
        });

        // 监听选择框变化
        document.getElementById('batchOtaSelect').addEventListener('change', function(e) {
            if (e.target.value) {
                console.log('选择的OTA渠道:', e.target.value);
            }
        });
    </script>
</body>
</html>
