# 监听器代码实现示例

## 1. 自动保存监听器实现

### 1.1 自动保存监听器

```javascript
/**
 * 自动保存监听器
 * @LISTENER @AUTO_SAVE
 */
import { debounce } from '../utils/debounce.js';

class AutoSaveListener {
    constructor(formManager, storageManager) {
        this.formManager = formManager;
        this.storageManager = storageManager;
        this.saveInterval = 30000; // 30秒自动保存
        this.debouncedSave = debounce(this.saveFormData.bind(this), 2000);
        this.autoSaveTimer = null;
        this.isEnabled = true;
        this.lastSaveTime = null;
        this.saveKey = 'autoSave_orderForm';
        
        console.log('[AutoSaveListener] 自动保存监听器已创建');
    }
    
    /**
     * 初始化监听器
     */
    initialize() {
        try {
            this.setupFormListeners();
            this.startAutoSave();
            this.restoreFormData();
            console.log('[AutoSaveListener] 自动保存监听器初始化完成');
        } catch (error) {
            console.error('[AutoSaveListener] 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 设置表单监听器
     */
    setupFormListeners() {
        const form = document.getElementById('orderForm');
        if (!form) {
            throw new Error('找不到订单表单');
        }
        
        // 监听所有输入变化
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', this.debouncedSave);
            input.addEventListener('change', this.debouncedSave);
        });
        
        // 监听页面离开事件
        window.addEventListener('beforeunload', (e) => {
            this.saveFormData(true); // 强制保存
        });
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.saveFormData(true);
            }
        });
        
        console.log(`[AutoSaveListener] 已为 ${inputs.length} 个字段设置自动保存`);
    }
    
    /**
     * 开始自动保存
     */
    startAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }
        
        this.autoSaveTimer = setInterval(() => {
            if (this.isEnabled) {
                this.saveFormData();
            }
        }, this.saveInterval);
        
        console.log(`[AutoSaveListener] 自动保存已启动，间隔: ${this.saveInterval}ms`);
    }
    
    /**
     * 停止自动保存
     */
    stopAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }
        console.log('[AutoSaveListener] 自动保存已停止');
    }
    
    /**
     * 保存表单数据
     * @param {boolean} force - 是否强制保存
     */
    async saveFormData(force = false) {
        if (!this.isEnabled && !force) return;
        
        try {
            const form = document.getElementById('orderForm');
            if (!form) return;
            
            const formData = new FormData(form);
            const data = {
                timestamp: Date.now(),
                data: Object.fromEntries(formData.entries()),
                url: window.location.href,
                sessionId: this.getSessionId()
            };
            
            // 检查数据是否有变化
            if (!force && !this.hasDataChanged(data)) {
                return;
            }
            
            // 保存到本地存储
            await this.storageManager.setItem(this.saveKey, data);
            
            // 保存到服务器（如果配置了）
            if (this.shouldSaveToServer()) {
                await this.saveToServer(data);
            }
            
            this.lastSaveTime = Date.now();
            
            // 显示保存提示
            this.showSaveIndicator();
            
            // 触发保存事件
            this.dispatchSaveEvent('autoSaved', data);
            
            console.log('[AutoSaveListener] 表单数据已自动保存');
            
        } catch (error) {
            console.error('[AutoSaveListener] 保存失败:', error);
            this.handleSaveError(error);
        }
    }
    
    /**
     * 恢复表单数据
     */
    async restoreFormData() {
        try {
            const savedData = await this.storageManager.getItem(this.saveKey);
            
            if (!savedData || !savedData.data) {
                console.log('[AutoSaveListener] 没有找到保存的数据');
                return;
            }
            
            // 检查数据是否过期（24小时）
            const dataAge = Date.now() - savedData.timestamp;
            if (dataAge > 24 * 60 * 60 * 1000) {
                console.log('[AutoSaveListener] 保存的数据已过期');
                await this.clearSavedData();
                return;
            }
            
            // 询问用户是否恢复数据
            const shouldRestore = await this.confirmRestore(savedData);
            if (!shouldRestore) {
                return;
            }
            
            // 恢复表单数据
            this.fillFormData(savedData.data);
            
            // 显示恢复提示
            this.showRestoreIndicator();
            
            console.log('[AutoSaveListener] 表单数据已恢复');
            
        } catch (error) {
            console.error('[AutoSaveListener] 恢复数据失败:', error);
        }
    }
    
    /**
     * 填充表单数据
     * @param {Object} data - 表单数据
     */
    fillFormData(data) {
        const form = document.getElementById('orderForm');
        if (!form) return;
        
        for (const [name, value] of Object.entries(data)) {
            const field = form.querySelector(`[name="${name}"]`);
            if (field) {
                if (field.type === 'checkbox' || field.type === 'radio') {
                    field.checked = value === 'on' || value === field.value;
                } else {
                    field.value = value;
                }
                
                // 触发change事件以更新UI
                field.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }
    }
    
    /**
     * 检查数据是否有变化
     * @param {Object} newData - 新数据
     */
    hasDataChanged(newData) {
        // 简单的数据比较，实际项目中可能需要更复杂的比较逻辑
        const lastData = this.lastSavedData;
        if (!lastData) return true;
        
        return JSON.stringify(newData.data) !== JSON.stringify(lastData.data);
    }
    
    /**
     * 获取会话ID
     */
    getSessionId() {
        let sessionId = sessionStorage.getItem('autoSaveSessionId');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('autoSaveSessionId', sessionId);
        }
        return sessionId;
    }
    
    /**
     * 是否应该保存到服务器
     */
    shouldSaveToServer() {
        // 检查是否有网络连接和服务器配置
        return navigator.onLine && window.OTA?.config?.autoSave?.serverEnabled;
    }
    
    /**
     * 保存到服务器
     * @param {Object} data - 数据
     */
    async saveToServer(data) {
        const endpoint = window.OTA?.config?.autoSave?.endpoint || '/api/autosave';
        
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        if (!response.ok) {
            throw new Error(`服务器保存失败: ${response.status}`);
        }
        
        return response.json();
    }
    
    /**
     * 确认是否恢复数据
     * @param {Object} savedData - 保存的数据
     */
    async confirmRestore(savedData) {
        const saveTime = new Date(savedData.timestamp).toLocaleString();
        const message = `发现未完成的表单数据（保存时间: ${saveTime}），是否恢复？`;
        
        // 使用UI管理器显示确认对话框
        if (window.OTA?.managers?.ui?.showConfirm) {
            return await window.OTA.managers.ui.showConfirm(message);
        } else {
            return confirm(message);
        }
    }
    
    /**
     * 显示保存指示器
     */
    showSaveIndicator() {
        // 创建或更新保存指示器
        let indicator = document.getElementById('autoSaveIndicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'autoSaveIndicator';
            indicator.className = 'fixed top-4 right-4 bg-green-500 text-white px-3 py-1 rounded text-sm z-50';
            document.body.appendChild(indicator);
        }
        
        indicator.textContent = '已自动保存';
        indicator.style.display = 'block';
        
        // 3秒后隐藏
        setTimeout(() => {
            indicator.style.display = 'none';
        }, 3000);
    }
    
    /**
     * 显示恢复指示器
     */
    showRestoreIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'fixed top-4 right-4 bg-blue-500 text-white px-3 py-1 rounded text-sm z-50';
        indicator.textContent = '数据已恢复';
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            indicator.remove();
        }, 5000);
    }
    
    /**
     * 处理保存错误
     * @param {Error} error - 错误对象
     */
    handleSaveError(error) {
        console.error('[AutoSaveListener] 保存错误:', error);
        
        // 显示错误提示
        if (window.OTA?.managers?.ui?.showAlert) {
            window.OTA.managers.ui.showAlert('自动保存失败，请手动保存表单', 'warning');
        }
        
        // 触发错误事件
        this.dispatchSaveEvent('saveError', { error: error.message });
    }
    
    /**
     * 分发保存事件
     * @param {string} eventType - 事件类型
     * @param {Object} detail - 事件详情
     */
    dispatchSaveEvent(eventType, detail) {
        const event = new CustomEvent(`autoSave:${eventType}`, {
            detail,
            bubbles: true
        });
        document.dispatchEvent(event);
    }
    
    /**
     * 清除保存的数据
     */
    async clearSavedData() {
        try {
            await this.storageManager.removeItem(this.saveKey);
            console.log('[AutoSaveListener] 保存的数据已清除');
        } catch (error) {
            console.error('[AutoSaveListener] 清除数据失败:', error);
        }
    }
    
    /**
     * 启用自动保存
     */
    enable() {
        this.isEnabled = true;
        console.log('[AutoSaveListener] 自动保存已启用');
    }
    
    /**
     * 禁用自动保存
     */
    disable() {
        this.isEnabled = false;
        console.log('[AutoSaveListener] 自动保存已禁用');
    }
    
    /**
     * 获取保存状态
     */
    getStatus() {
        return {
            isEnabled: this.isEnabled,
            lastSaveTime: this.lastSaveTime,
            saveInterval: this.saveInterval,
            hasAutoSaveTimer: !!this.autoSaveTimer
        };
    }
    
    /**
     * 销毁监听器
     */
    destroy() {
        this.stopAutoSave();
        this.isEnabled = false;
        
        // 最后一次保存
        this.saveFormData(true);
        
        console.log('[AutoSaveListener] 自动保存监听器已销毁');
    }
}

export default AutoSaveListener;
```

## 2. 拖拽上传监听器实现

### 2.1 拖拽上传监听器

```javascript
/**
 * 拖拽上传监听器
 * @LISTENER @DRAG_DROP_UPLOAD
 */
class DragDropUploadListener {
    constructor(uploadManager) {
        this.uploadManager = uploadManager;
        this.dropZones = new Map(); // 存储拖拽区域
        this.dragCounter = 0; // 拖拽计数器
        this.allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        this.maxFiles = 5;
        
        console.log('[DragDropUploadListener] 拖拽上传监听器已创建');
    }
    
    /**
     * 初始化监听器
     */
    initialize() {
        try {
            this.setupDropZones();
            this.setupGlobalListeners();
            console.log('[DragDropUploadListener] 拖拽上传监听器初始化完成');
        } catch (error) {
            console.error('[DragDropUploadListener] 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 设置拖拽区域
     */
    setupDropZones() {
        // 查找所有拖拽区域
        const dropZones = document.querySelectorAll('[data-drop-zone]');
        
        dropZones.forEach(zone => {
            this.addDropZone(zone);
        });
        
        // 如果没有指定的拖拽区域，使用整个文档
        if (dropZones.length === 0) {
            this.addDropZone(document.body, true);
        }
        
        console.log(`[DragDropUploadListener] 已设置 ${dropZones.length} 个拖拽区域`);
    }
    
    /**
     * 添加拖拽区域
     * @param {HTMLElement} element - 拖拽区域元素
     * @param {boolean} isGlobal - 是否为全局区域
     */
    addDropZone(element, isGlobal = false) {
        const zoneId = element.id || `dropZone_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const zoneConfig = {
            element,
            isGlobal,
            allowedTypes: this.parseAllowedTypes(element),
            maxFileSize: this.parseMaxFileSize(element),
            maxFiles: this.parseMaxFiles(element)
        };
        
        // 添加事件监听器
        element.addEventListener('dragenter', (e) => this.handleDragEnter(e, zoneId));
        element.addEventListener('dragover', (e) => this.handleDragOver(e, zoneId));
        element.addEventListener('dragleave', (e) => this.handleDragLeave(e, zoneId));
        element.addEventListener('drop', (e) => this.handleDrop(e, zoneId));
        
        this.dropZones.set(zoneId, zoneConfig);
        
        // 添加视觉样式
        if (!isGlobal) {
            element.classList.add('drop-zone');
        }
        
        console.log(`[DragDropUploadListener] 拖拽区域 "${zoneId}" 已添加`);
    }
    
    /**
     * 设置全局监听器
     */
    setupGlobalListeners() {
        // 防止默认的拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            }, false);
        });
        
        // 全局拖拽进入
        document.addEventListener('dragenter', (e) => {
            this.dragCounter++;
            this.showGlobalDropIndicator();
        });
        
        // 全局拖拽离开
        document.addEventListener('dragleave', (e) => {
            this.dragCounter--;
            if (this.dragCounter === 0) {
                this.hideGlobalDropIndicator();
            }
        });
        
        // 全局放置
        document.addEventListener('drop', (e) => {
            this.dragCounter = 0;
            this.hideGlobalDropIndicator();
        });
    }
    
    /**
     * 处理拖拽进入
     * @param {DragEvent} e - 拖拽事件
     * @param {string} zoneId - 区域ID
     */
    handleDragEnter(e, zoneId) {
        e.preventDefault();
        e.stopPropagation();
        
        const zone = this.dropZones.get(zoneId);
        if (!zone) return;
        
        // 检查是否包含文件
        if (!this.hasFiles(e.dataTransfer)) {
            return;
        }
        
        // 添加拖拽样式
        zone.element.classList.add('drag-over');
        
        // 显示拖拽提示
        this.showDropHint(zone.element);
        
        console.log(`[DragDropUploadListener] 拖拽进入区域: ${zoneId}`);
    }
    
    /**
     * 处理拖拽悬停
     * @param {DragEvent} e - 拖拽事件
     * @param {string} zoneId - 区域ID
     */
    handleDragOver(e, zoneId) {
        e.preventDefault();
        e.stopPropagation();
        
        // 设置拖拽效果
        e.dataTransfer.dropEffect = 'copy';
    }
    
    /**
     * 处理拖拽离开
     * @param {DragEvent} e - 拖拽事件
     * @param {string} zoneId - 区域ID
     */
    handleDragLeave(e, zoneId) {
        e.preventDefault();
        e.stopPropagation();
        
        const zone = this.dropZones.get(zoneId);
        if (!zone) return;
        
        // 检查是否真的离开了区域
        if (!zone.element.contains(e.relatedTarget)) {
            zone.element.classList.remove('drag-over');
            this.hideDropHint(zone.element);
        }
    }
    
    /**
     * 处理文件放置
     * @param {DragEvent} e - 拖拽事件
     * @param {string} zoneId - 区域ID
     */
    async handleDrop(e, zoneId) {
        e.preventDefault();
        e.stopPropagation();
        
        const zone = this.dropZones.get(zoneId);
        if (!zone) return;
        
        // 移除拖拽样式
        zone.element.classList.remove('drag-over');
        this.hideDropHint(zone.element);
        
        const files = Array.from(e.dataTransfer.files);
        
        if (files.length === 0) {
            console.log('[DragDropUploadListener] 没有检测到文件');
            return;
        }
        
        console.log(`[DragDropUploadListener] 检测到 ${files.length} 个文件`);
        
        try {
            // 验证文件
            const validFiles = await this.validateFiles(files, zone);
            
            if (validFiles.length === 0) {
                this.showError('没有有效的文件可以上传');
                return;
            }
            
            // 开始上传
            await this.uploadFiles(validFiles, zone);
            
        } catch (error) {
            console.error('[DragDropUploadListener] 处理文件失败:', error);
            this.showError('文件处理失败: ' + error.message);
        }
    }
    
    /**
     * 验证文件
     * @param {File[]} files - 文件列表
     * @param {Object} zone - 拖拽区域配置
     */
    async validateFiles(files, zone) {
        const validFiles = [];
        const errors = [];
        
        // 检查文件数量
        if (files.length > zone.maxFiles) {
            errors.push(`最多只能上传 ${zone.maxFiles} 个文件`);
            files = files.slice(0, zone.maxFiles);
        }
        
        for (const file of files) {
            const validation = await this.validateSingleFile(file, zone);
            
            if (validation.isValid) {
                validFiles.push(file);
            } else {
                errors.push(`${file.name}: ${validation.error}`);
            }
        }
        
        // 显示验证错误
        if (errors.length > 0) {
            this.showValidationErrors(errors);
        }
        
        return validFiles;
    }
    
    /**
     * 验证单个文件
     * @param {File} file - 文件
     * @param {Object} zone - 拖拽区域配置
     */
    async validateSingleFile(file, zone) {
        // 检查文件类型
        if (!zone.allowedTypes.includes(file.type)) {
            return {
                isValid: false,
                error: `不支持的文件类型: ${file.type}`
            };
        }
        
        // 检查文件大小
        if (file.size > zone.maxFileSize) {
            return {
                isValid: false,
                error: `文件过大: ${this.formatFileSize(file.size)} (最大: ${this.formatFileSize(zone.maxFileSize)})`
            };
        }
        
        // 检查文件名
        if (file.name.length > 255) {
            return {
                isValid: false,
                error: '文件名过长'
            };
        }
        
        // 检查是否为空文件
        if (file.size === 0) {
            return {
                isValid: false,
                error: '文件为空'
            };
        }
        
        // 额外的文件内容验证（如果是图片）
        if (file.type.startsWith('image/')) {
            const isValidImage = await this.validateImageFile(file);
            if (!isValidImage) {
                return {
                    isValid: false,
                    error: '无效的图片文件'
                };
            }
        }
        
        return { isValid: true };
    }
    
    /**
     * 验证图片文件
     * @param {File} file - 图片文件
     */
    validateImageFile(file) {
        return new Promise((resolve) => {
            const img = new Image();
            const url = URL.createObjectURL(file);
            
            img.onload = () => {
                URL.revokeObjectURL(url);
                
                // 检查图片尺寸
                const maxWidth = 4000;
                const maxHeight = 4000;
                
                if (img.width > maxWidth || img.height > maxHeight) {
                    resolve(false);
                } else {
                    resolve(true);
                }
            };
            
            img.onerror = () => {
                URL.revokeObjectURL(url);
                resolve(false);
            };
            
            img.src = url;
        });
    }
    
    /**
     * 上传文件
     * @param {File[]} files - 文件列表
     * @param {Object} zone - 拖拽区域配置
     */
    async uploadFiles(files, zone) {
        // 显示上传进度
        const progressContainer = this.createProgressContainer(zone.element);
        
        try {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                
                // 创建文件进度项
                const progressItem = this.createProgressItem(file, progressContainer);
                
                try {
                    // 上传文件
                    const result = await this.uploadSingleFile(file, (progress) => {
                        this.updateProgress(progressItem, progress);
                    });
                    
                    // 上传成功
                    this.markProgressComplete(progressItem, result);
                    
                    // 触发上传成功事件
                    this.dispatchUploadEvent('fileUploaded', {
                        file,
                        result,
                        zoneId: zone.element.id
                    });
                    
                } catch (error) {
                    // 上传失败
                    this.markProgressError(progressItem, error.message);
                    
                    // 触发上传失败事件
                    this.dispatchUploadEvent('fileUploadError', {
                        file,
                        error: error.message,
                        zoneId: zone.element.id
                    });
                }
            }
            
            // 所有文件处理完成
            setTimeout(() => {
                progressContainer.remove();
            }, 3000);
            
        } catch (error) {
            console.error('[DragDropUploadListener] 批量上传失败:', error);
            progressContainer.remove();
            this.showError('批量上传失败: ' + error.message);
        }
    }
    
    /**
     * 上传单个文件
     * @param {File} file - 文件
     * @param {Function} onProgress - 进度回调
     */
    uploadSingleFile(file, onProgress) {
        return new Promise((resolve, reject) => {
            // 使用上传管理器上传文件
            if (this.uploadManager && typeof this.uploadManager.uploadFile === 'function') {
                this.uploadManager.uploadFile(file, {
                    onProgress,
                    onSuccess: resolve,
                    onError: reject
                });
            } else {
                // 降级到基本的fetch上传
                this.basicUpload(file, onProgress)
                    .then(resolve)
                    .catch(reject);
            }
        });
    }
    
    /**
     * 基本上传实现
     * @param {File} file - 文件
     * @param {Function} onProgress - 进度回调
     */
    basicUpload(file, onProgress) {
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file', file);
            
            const xhr = new XMLHttpRequest();
            
            // 上传进度
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const progress = (e.loaded / e.total) * 100;
                    onProgress(progress);
                }
            });
            
            // 上传完成
            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        resolve({ success: true, message: '上传成功' });
                    }
                } else {
                    reject(new Error(`上传失败: ${xhr.status} ${xhr.statusText}`));
                }
            });
            
            // 上传错误
            xhr.addEventListener('error', () => {
                reject(new Error('网络错误'));
            });
            
            // 上传超时
            xhr.addEventListener('timeout', () => {
                reject(new Error('上传超时'));
            });
            
            // 设置超时时间
            xhr.timeout = 60000; // 60秒
            
            // 发送请求
            const uploadUrl = window.OTA?.config?.upload?.endpoint || '/api/upload';
            xhr.open('POST', uploadUrl);
            xhr.send(formData);
        });
    }
    
    /**
     * 检查是否包含文件
     * @param {DataTransfer} dataTransfer - 数据传输对象
     */
    hasFiles(dataTransfer) {
        return dataTransfer.types && dataTransfer.types.includes('Files');
    }
    
    /**
     * 解析允许的文件类型
     * @param {HTMLElement} element - 元素
     */
    parseAllowedTypes(element) {
        const types = element.dataset.allowedTypes;
        return types ? types.split(',').map(type => type.trim()) : this.allowedTypes;
    }
    
    /**
     * 解析最大文件大小
     * @param {HTMLElement} element - 元素
     */
    parseMaxFileSize(element) {
        const size = element.dataset.maxFileSize;
        return size ? parseInt(size) : this.maxFileSize;
    }
    
    /**
     * 解析最大文件数量
     * @param {HTMLElement} element - 元素
     */
    parseMaxFiles(element) {
        const count = element.dataset.maxFiles;
        return count ? parseInt(count) : this.maxFiles;
    }
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 显示全局拖拽指示器
     */
    showGlobalDropIndicator() {
        let indicator = document.getElementById('globalDropIndicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'globalDropIndicator';
            indicator.className = 'fixed inset-0 bg-blue-500 bg-opacity-20 border-4 border-dashed border-blue-500 z-50 flex items-center justify-center';
            indicator.innerHTML = `
                <div class="bg-white rounded-lg p-6 shadow-lg text-center">
                    <div class="text-4xl mb-4">📁</div>
                    <div class="text-xl font-semibold text-gray-800">拖拽文件到此处上传</div>
                    <div class="text-sm text-gray-600 mt-2">支持图片和PDF文件</div>
                </div>
            `;
            document.body.appendChild(indicator);
        }
        indicator.style.display = 'flex';
    }
    
    /**
     * 隐藏全局拖拽指示器
     */
    hideGlobalDropIndicator() {
        const indicator = document.getElementById('globalDropIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    
    /**
     * 显示拖拽提示
     * @param {HTMLElement} element - 拖拽区域元素
     */
    showDropHint(element) {
        let hint = element.querySelector('.drop-hint');
        if (!hint) {
            hint = document.createElement('div');
            hint.className = 'drop-hint absolute inset-0 bg-blue-500 bg-opacity-10 border-2 border-dashed border-blue-500 flex items-center justify-center z-10';
            hint.innerHTML = '<div class="text-blue-600 font-semibold">释放文件开始上传</div>';
            element.style.position = 'relative';
            element.appendChild(hint);
        }
        hint.style.display = 'flex';
    }
    
    /**
     * 隐藏拖拽提示
     * @param {HTMLElement} element - 拖拽区域元素
     */
    hideDropHint(element) {
        const hint = element.querySelector('.drop-hint');
        if (hint) {
            hint.style.display = 'none';
        }
    }
    
    /**
     * 创建进度容器
     * @param {HTMLElement} zone - 拖拽区域
     */
    createProgressContainer(zone) {
        const container = document.createElement('div');
        container.className = 'upload-progress-container fixed top-4 right-4 bg-white rounded-lg shadow-lg p-4 z-50 max-w-sm';
        container.innerHTML = `
            <div class="flex items-center justify-between mb-3">
                <h3 class="font-semibold text-gray-800">文件上传</h3>
                <button class="text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                    ✕
                </button>
            </div>
            <div class="upload-items"></div>
        `;
        
        document.body.appendChild(container);
        return container;
    }
    
    /**
     * 创建进度项
     * @param {File} file - 文件
     * @param {HTMLElement} container - 容器
     */
    createProgressItem(file, container) {
        const item = document.createElement('div');
        item.className = 'upload-item mb-3 p-2 border rounded';
        item.innerHTML = `
            <div class="flex items-center justify-between mb-1">
                <span class="text-sm font-medium text-gray-700 truncate">${file.name}</span>
                <span class="text-xs text-gray-500">${this.formatFileSize(file.size)}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
            <div class="text-xs text-gray-500 mt-1">准备上传...</div>
        `;
        
        container.querySelector('.upload-items').appendChild(item);
        return item;
    }
    
    /**
     * 更新进度
     * @param {HTMLElement} item - 进度项
     * @param {number} progress - 进度百分比
     */
    updateProgress(item, progress) {
        const progressBar = item.querySelector('.bg-blue-600');
        const statusText = item.querySelector('.text-xs');
        
        progressBar.style.width = `${progress}%`;
        statusText.textContent = `上传中... ${Math.round(progress)}%`;
    }
    
    /**
     * 标记进度完成
     * @param {HTMLElement} item - 进度项
     * @param {Object} result - 上传结果
     */
    markProgressComplete(item, result) {
        const progressBar = item.querySelector('.bg-blue-600');
        const statusText = item.querySelector('.text-xs');
        
        progressBar.style.width = '100%';
        progressBar.className = 'bg-green-600 h-2 rounded-full transition-all duration-300';
        statusText.textContent = '上传成功';
        statusText.className = 'text-xs text-green-600 mt-1';
    }
    
    /**
     * 标记进度错误
     * @param {HTMLElement} item - 进度项
     * @param {string} error - 错误信息
     */
    markProgressError(item, error) {
        const progressBar = item.querySelector('.bg-blue-600');
        const statusText = item.querySelector('.text-xs');
        
        progressBar.className = 'bg-red-600 h-2 rounded-full transition-all duration-300';
        statusText.textContent = `上传失败: ${error}`;
        statusText.className = 'text-xs text-red-600 mt-1';
    }
    
    /**
     * 显示验证错误
     * @param {string[]} errors - 错误列表
     */
    showValidationErrors(errors) {
        const message = '文件验证失败:\n' + errors.join('\n');
        
        if (window.OTA?.managers?.ui?.showAlert) {
            window.OTA.managers.ui.showAlert(message, 'error');
        } else {
            alert(message);
        }
    }
    
    /**
     * 显示错误
     * @param {string} message - 错误消息
     */
    showError(message) {
        if (window.OTA?.managers?.ui?.showAlert) {
            window.OTA.managers.ui.showAlert(message, 'error');
        } else {
            alert(message);
        }
    }
    
    /**
     * 分发上传事件
     * @param {string} eventType - 事件类型
     * @param {Object} detail - 事件详情
     */
    dispatchUploadEvent(eventType, detail) {
        const event = new CustomEvent(`upload:${eventType}`, {
            detail,
            bubbles: true
        });
        document.dispatchEvent(event);
    }
    
    /**
     * 移除拖拽区域
     * @param {string} zoneId - 区域ID
     */
    removeDropZone(zoneId) {
        const zone = this.dropZones.get(zoneId);
        if (zone) {
            // 移除事件监听器（这里简化处理，实际项目中需要保存监听器引用）
            zone.element.classList.remove('drop-zone', 'drag-over');
            this.hideDropHint(zone.element);
            
            this.dropZones.delete(zoneId);
            console.log(`[DragDropUploadListener] 拖拽区域 "${zoneId}" 已移除`);
        }
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            totalDropZones: this.dropZones.size,
            allowedTypes: this.allowedTypes,
            maxFileSize: this.maxFileSize,
            maxFiles: this.maxFiles
        };
    }
    
    /**
     * 销毁监听器
     */
    destroy() {
        // 清理所有拖拽区域
        for (const [zoneId] of this.dropZones) {
            this.removeDropZone(zoneId);
        }
        
        // 隐藏全局指示器
        this.hideGlobalDropIndicator();
        
        // 重置计数器
        this.dragCounter = 0;
        
        console.log('[DragDropUploadListener] 拖拽上传监听器已销毁');
    }
}

export default DragDropUploadListener;
```

## 3. 网络状态监听器实现

### 3.1 网络状态监听器

```javascript
/**
 * 网络状态监听器
 * @LISTENER @NETWORK_STATUS
 */
class NetworkStatusListener {
    constructor() {
        this.isOnline = navigator.onLine;
        this.connectionType = this.getConnectionType();
        this.lastOnlineTime = this.isOnline ? Date.now() : null;
        this.lastOfflineTime = this.isOnline ? null : Date.now();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 初始重连延迟
        this.statusIndicator = null;
        
        console.log('[NetworkStatusListener] 网络状态监听器已创建');
    }
    
    /**
     * 初始化监听器
     */
    initialize() {
        try {
            this.setupEventListeners();
            this.createStatusIndicator();
            this.updateStatus();
            console.log('[NetworkStatusListener] 网络状态监听器初始化完成');
        } catch (error) {
            console.error('[NetworkStatusListener] 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听在线/离线状态变化
        window.addEventListener('online', this.handleOnline.bind(this));
        window.addEventListener('offline', this.handleOffline.bind(this));
        
        // 监听连接类型变化（如果支持）
        if ('connection' in navigator) {
            navigator.connection.removeEventListener('change', this.handleConnectionChange);
        }
        
        // 清理定时器
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
        }
        
        // 移除状态指示器
        if (this.statusIndicator) {
            this.statusIndicator.remove();
        }
        
        console.log('[NetworkStatusListener] 网络状态监听器已销毁');
    }
}

// 导出监听器
export default NetworkStatusListener;

// 注册到全局
if (typeof window !== 'undefined') {
    window.NetworkStatusListener = NetworkStatusListener;
}
```

## 4. 表单验证监听器实现

### 4.1 表单验证监听器

```javascript
/**
 * 表单验证监听器
 * @LISTENER @FORM_VALIDATION
 */
import { debounce } from '../utils/debounce.js';

class FormValidationListener {
    constructor(formSelector = '#orderForm') {
        this.formSelector = formSelector;
        this.form = null;
        this.validators = new Map();
        this.errors = new Map();
        this.isValidating = false;
        this.validationRules = new Map();
        this.debouncedValidate = debounce(this.validateField.bind(this), 300);
        
        console.log('[FormValidationListener] 表单验证监听器已创建');
    }
    
    /**
     * 初始化监听器
     */
    initialize() {
        try {
            this.form = document.querySelector(this.formSelector);
            if (!this.form) {
                throw new Error(`找不到表单: ${this.formSelector}`);
            }
            
            this.setupValidationRules();
            this.setupFormListeners();
            this.createErrorContainer();
            
            console.log('[FormValidationListener] 表单验证监听器初始化完成');
        } catch (error) {
            console.error('[FormValidationListener] 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 设置验证规则
     */
    setupValidationRules() {
        // 基础验证规则
        this.addValidationRule('required', (value, field) => {
            if (!value || value.trim() === '') {
                return `${this.getFieldLabel(field)}不能为空`;
            }
            return null;
        });
        
        this.addValidationRule('email', (value, field) => {
            if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                return '请输入有效的邮箱地址';
            }
            return null;
        });
        
        this.addValidationRule('phone', (value, field) => {
            if (value && !/^1[3-9]\d{9}$/.test(value)) {
                return '请输入有效的手机号码';
            }
            return null;
        });
        
        this.addValidationRule('minLength', (value, field, params) => {
            const minLength = params.minLength || 0;
            if (value && value.length < minLength) {
                return `${this.getFieldLabel(field)}至少需要${minLength}个字符`;
            }
            return null;
        });
        
        this.addValidationRule('maxLength', (value, field, params) => {
            const maxLength = params.maxLength || Infinity;
            if (value && value.length > maxLength) {
                return `${this.getFieldLabel(field)}不能超过${maxLength}个字符`;
            }
            return null;
        });
        
        this.addValidationRule('number', (value, field) => {
            if (value && isNaN(value)) {
                return `${this.getFieldLabel(field)}必须是数字`;
            }
            return null;
        });
        
        this.addValidationRule('min', (value, field, params) => {
            const min = params.min;
            if (value && Number(value) < min) {
                return `${this.getFieldLabel(field)}不能小于${min}`;
            }
            return null;
        });
        
        this.addValidationRule('max', (value, field, params) => {
            const max = params.max;
            if (value && Number(value) > max) {
                return `${this.getFieldLabel(field)}不能大于${max}`;
            }
            return null;
        });
        
        console.log('[FormValidationListener] 验证规则已设置');
    }
    
    /**
     * 设置表单监听器
     */
    setupFormListeners() {
        const fields = this.form.querySelectorAll('input, select, textarea');
        
        fields.forEach(field => {
            // 实时验证
            field.addEventListener('input', (e) => {
                this.debouncedValidate(e.target);
            });
            
            // 失焦验证
            field.addEventListener('blur', (e) => {
                this.validateField(e.target);
            });
            
            // 获得焦点时清除错误
            field.addEventListener('focus', (e) => {
                this.clearFieldError(e.target);
            });
        });
        
        // 表单提交验证
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.validateForm().then(isValid => {
                if (isValid) {
                    this.handleFormSubmit(e);
                } else {
                    this.focusFirstError();
                }
            });
        });
        
        console.log(`[FormValidationListener] 已为 ${fields.length} 个字段设置验证监听器`);
    }
    
    /**
     * 添加验证规则
     * @param {string} name - 规则名称
     * @param {Function} validator - 验证函数
     */
    addValidationRule(name, validator) {
        this.validators.set(name, validator);
    }
    
    /**
     * 为字段添加验证规则
     * @param {string} fieldName - 字段名称
     * @param {Array} rules - 验证规则数组
     */
    addFieldValidation(fieldName, rules) {
        this.validationRules.set(fieldName, rules);
    }
    
    /**
     * 验证单个字段
     * @param {HTMLElement} field - 字段元素
     */
    async validateField(field) {
        const fieldName = field.name || field.id;
        const value = field.value;
        const rules = this.validationRules.get(fieldName) || this.getFieldRulesFromAttributes(field);
        
        // 清除之前的错误
        this.clearFieldError(field);
        
        // 执行验证
        for (const rule of rules) {
            const validator = this.validators.get(rule.type);
            if (validator) {
                const error = await validator(value, field, rule.params || {});
                if (error) {
                    this.setFieldError(field, error);
                    return false;
                }
            }
        }
        
        // 标记字段为有效
        this.markFieldValid(field);
        return true;
    }
    
    /**
     * 验证整个表单
     */
    async validateForm() {
        this.isValidating = true;
        const fields = this.form.querySelectorAll('input, select, textarea');
        const validationPromises = Array.from(fields).map(field => this.validateField(field));
        
        const results = await Promise.all(validationPromises);
        this.isValidating = false;
        
        const isValid = results.every(result => result === true);
        
        // 触发验证完成事件
        this.dispatchValidationEvent('formValidated', {
            isValid,
            errors: Array.from(this.errors.entries())
        });
        
        return isValid;
    }
    
    /**
     * 从字段属性获取验证规则
     * @param {HTMLElement} field - 字段元素
     */
    getFieldRulesFromAttributes(field) {
        const rules = [];
        
        // required 属性
        if (field.hasAttribute('required')) {
            rules.push({ type: 'required' });
        }
        
        // type 属性
        if (field.type === 'email') {
            rules.push({ type: 'email' });
        }
        
        if (field.type === 'number') {
            rules.push({ type: 'number' });
            
            if (field.hasAttribute('min')) {
                rules.push({ type: 'min', params: { min: Number(field.getAttribute('min')) } });
            }
            
            if (field.hasAttribute('max')) {
                rules.push({ type: 'max', params: { max: Number(field.getAttribute('max')) } });
            }
        }
        
        // minlength 和 maxlength 属性
        if (field.hasAttribute('minlength')) {
            rules.push({ type: 'minLength', params: { minLength: Number(field.getAttribute('minlength')) } });
        }
        
        if (field.hasAttribute('maxlength')) {
            rules.push({ type: 'maxLength', params: { maxLength: Number(field.getAttribute('maxlength')) } });
        }
        
        // 自定义验证属性
        if (field.hasAttribute('data-validation')) {
            const customRules = field.getAttribute('data-validation').split(',');
            customRules.forEach(rule => {
                rules.push({ type: rule.trim() });
            });
        }
        
        return rules;
    }
    
    /**
     * 设置字段错误
     * @param {HTMLElement} field - 字段元素
     * @param {string} message - 错误消息
     */
    setFieldError(field, message) {
        const fieldName = field.name || field.id;
        this.errors.set(fieldName, message);
        
        // 添加错误样式
        field.classList.add('border-red-500', 'focus:border-red-500');
        field.classList.remove('border-green-500', 'focus:border-green-500');
        
        // 显示错误消息
        this.showFieldError(field, message);
        
        // 触发字段错误事件
        this.dispatchValidationEvent('fieldError', {
            field: fieldName,
            message,
            element: field
        });
    }
    
    /**
     * 清除字段错误
     * @param {HTMLElement} field - 字段元素
     */
    clearFieldError(field) {
        const fieldName = field.name || field.id;
        this.errors.delete(fieldName);
        
        // 移除错误样式
        field.classList.remove('border-red-500', 'focus:border-red-500');
        
        // 隐藏错误消息
        this.hideFieldError(field);
    }
    
    /**
     * 标记字段为有效
     * @param {HTMLElement} field - 字段元素
     */
    markFieldValid(field) {
        field.classList.add('border-green-500');
        field.classList.remove('border-red-500');
    }
    
    /**
     * 显示字段错误消息
     * @param {HTMLElement} field - 字段元素
     * @param {string} message - 错误消息
     */
    showFieldError(field, message) {
        const errorId = `error-${field.name || field.id}`;
        let errorElement = document.getElementById(errorId);
        
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.id = errorId;
            errorElement.className = 'text-red-500 text-sm mt-1';
            
            // 插入到字段后面
            const parent = field.parentNode;
            parent.insertBefore(errorElement, field.nextSibling);
        }
        
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }
    
    /**
     * 隐藏字段错误消息
     * @param {HTMLElement} field - 字段元素
     */
    hideFieldError(field) {
        const errorId = `error-${field.name || field.id}`;
        const errorElement = document.getElementById(errorId);
        
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }
    
    /**
     * 创建错误容器
     */
    createErrorContainer() {
        if (document.getElementById('formErrorSummary')) return;
        
        const errorContainer = document.createElement('div');
        errorContainer.id = 'formErrorSummary';
        errorContainer.className = 'bg-red-50 border border-red-200 rounded p-4 mb-4 hidden';
        
        const errorTitle = document.createElement('h4');
        errorTitle.className = 'text-red-800 font-medium mb-2';
        errorTitle.textContent = '请修正以下错误：';
        
        const errorList = document.createElement('ul');
        errorList.className = 'text-red-700 text-sm space-y-1';
        errorList.id = 'formErrorList';
        
        errorContainer.appendChild(errorTitle);
        errorContainer.appendChild(errorList);
        
        // 插入到表单顶部
        this.form.insertBefore(errorContainer, this.form.firstChild);
    }
    
    /**
     * 获取字段标签
     * @param {HTMLElement} field - 字段元素
     */
    getFieldLabel(field) {
        const label = this.form.querySelector(`label[for="${field.id}"]`);
        if (label) {
            return label.textContent.replace('*', '').trim();
        }
        
        return field.getAttribute('placeholder') || field.name || '该字段';
    }
    
    /**
     * 聚焦到第一个错误字段
     */
    focusFirstError() {
        const firstErrorField = this.form.querySelector('.border-red-500');
        if (firstErrorField) {
            firstErrorField.focus();
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
    
    /**
     * 处理表单提交
     * @param {Event} event - 提交事件
     */
    handleFormSubmit(event) {
        // 触发表单提交事件
        this.dispatchValidationEvent('formSubmit', {
            form: this.form,
            data: new FormData(this.form)
        });
        
        console.log('[FormValidationListener] 表单验证通过，准备提交');
    }
    
    /**
     * 分发验证事件
     * @param {string} eventType - 事件类型
     * @param {Object} detail - 事件详情
     */
    dispatchValidationEvent(eventType, detail) {
        const event = new CustomEvent(`validation:${eventType}`, {
            detail,
            bubbles: true
        });
        this.form.dispatchEvent(event);
    }
    
    /**
     * 获取验证状态
     */
    getValidationStatus() {
        return {
            isValid: this.errors.size === 0,
            errors: Array.from(this.errors.entries()),
            isValidating: this.isValidating
        };
    }
    
    /**
     * 重置验证状态
     */
    reset() {
        this.errors.clear();
        
        // 清除所有字段的错误状态
        const fields = this.form.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            this.clearFieldError(field);
            field.classList.remove('border-green-500', 'border-red-500');
        });
        
        // 隐藏错误摘要
        const errorSummary = document.getElementById('formErrorSummary');
        if (errorSummary) {
            errorSummary.classList.add('hidden');
        }
        
        console.log('[FormValidationListener] 验证状态已重置');
    }
    
    /**
     * 销毁监听器
     */
    destroy() {
        // 移除事件监听器
        const fields = this.form.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            field.removeEventListener('input', this.debouncedValidate);
            field.removeEventListener('blur', this.validateField);
            field.removeEventListener('focus', this.clearFieldError);
        });
        
        // 清理数据
        this.validators.clear();
        this.errors.clear();
        this.validationRules.clear();
        
        console.log('[FormValidationListener] 表单验证监听器已销毁');
    }
}

// 导出监听器
export default FormValidationListener;

// 注册到全局
if (typeof window !== 'undefined') {
    window.FormValidationListener = FormValidationListener;
}
```

## 5. 监听器注册中心实现

### 5.1 监听器注册中心

```javascript
/**
 * 监听器注册中心
 * @REGISTRY @LISTENER_MANAGER
 */
class ListenerRegistry {
    constructor() {
        this.listeners = new Map();
        this.eventBus = new EventTarget();
        this.isInitialized = false;
        this.config = {
            autoStart: true,
            errorHandling: 'log', // 'log', 'throw', 'silent'
            maxRetries: 3,
            retryDelay: 1000
        };
        
        console.log('[ListenerRegistry] 监听器注册中心已创建');
    }
    
    /**
     * 初始化注册中心
     */
    async initialize() {
        if (this.isInitialized) {
            console.warn('[ListenerRegistry] 注册中心已经初始化');
            return;
        }
        
        try {
            // 注册核心监听器
            await this.registerCoreListeners();
            
            // 启动自动启动的监听器
            if (this.config.autoStart) {
                await this.startAutoStartListeners();
            }
            
            this.isInitialized = true;
            console.log('[ListenerRegistry] 监听器注册中心初始化完成');
            
        } catch (error) {
            console.error('[ListenerRegistry] 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 注册监听器
     * @param {string} name - 监听器名称
     * @param {Object} listenerClass - 监听器类
     * @param {Object} options - 配置选项
     */
    register(name, listenerClass, options = {}) {
        if (this.listeners.has(name)) {
            console.warn(`[ListenerRegistry] 监听器 ${name} 已存在，将被覆盖`);
        }
        
        const listenerConfig = {
            name,
            class: listenerClass,
            instance: null,
            isActive: false,
            autoStart: options.autoStart || false,
            dependencies: options.dependencies || [],
            priority: options.priority || 0,
            retryCount: 0,
            lastError: null,
            ...options
        };
        
        this.listeners.set(name, listenerConfig);
        
        console.log(`[ListenerRegistry] 监听器 ${name} 已注册`);
        
        // 触发注册事件
        this.dispatchEvent('listenerRegistered', { name, config: listenerConfig });
        
        return this;
    }
    
    /**
     * 启动监听器
     * @param {string} name - 监听器名称
     * @param {Array} args - 构造函数参数
     */
    async start(name, ...args) {
        const config = this.listeners.get(name);
        if (!config) {
            throw new Error(`监听器 ${name} 未注册`);
        }
        
        if (config.isActive) {
            console.warn(`[ListenerRegistry] 监听器 ${name} 已经启动`);
            return config.instance;
        }
        
        try {
            // 检查依赖
            await this.checkDependencies(config);
            
            // 创建实例
            config.instance = new config.class(...args);
            
            // 初始化监听器
            if (typeof config.instance.initialize === 'function') {
                await config.instance.initialize();
            }
            
            config.isActive = true;
            config.retryCount = 0;
            config.lastError = null;
            
            console.log(`[ListenerRegistry] 监听器 ${name} 已启动`);
            
            // 触发启动事件
            this.dispatchEvent('listenerStarted', { name, instance: config.instance });
            
            return config.instance;
            
        } catch (error) {
            config.lastError = error;
            config.retryCount++;
            
            console.error(`[ListenerRegistry] 启动监听器 ${name} 失败:`, error);
            
            // 重试机制
            if (config.retryCount < this.config.maxRetries) {
                console.log(`[ListenerRegistry] 将在 ${this.config.retryDelay}ms 后重试启动 ${name}`);
                
                setTimeout(() => {
                    this.start(name, ...args);
                }, this.config.retryDelay);
            } else {
                this.handleError(error, `启动监听器 ${name}`);
            }
            
            throw error;
        }
    }
    
    /**
     * 停止监听器
     * @param {string} name - 监听器名称
     */
    async stop(name) {
        const config = this.listeners.get(name);
        if (!config) {
            throw new Error(`监听器 ${name} 未注册`);
        }
        
        if (!config.isActive) {
            console.warn(`[ListenerRegistry] 监听器 ${name} 未启动`);
            return;
        }
        
        try {
            // 销毁监听器
            if (config.instance && typeof config.instance.destroy === 'function') {
                await config.instance.destroy();
            }
            
            config.instance = null;
            config.isActive = false;
            
            console.log(`[ListenerRegistry] 监听器 ${name} 已停止`);
            
            // 触发停止事件
            this.dispatchEvent('listenerStopped', { name });
            
        } catch (error) {
            console.error(`[ListenerRegistry] 停止监听器 ${name} 失败:`, error);
            this.handleError(error, `停止监听器 ${name}`);
            throw error;
        }
    }
    
    /**
     * 重启监听器
     * @param {string} name - 监听器名称
     * @param {Array} args - 构造函数参数
     */
    async restart(name, ...args) {
        await this.stop(name);
        return await this.start(name, ...args);
    }
    
    /**
     * 获取监听器实例
     * @param {string} name - 监听器名称
     */
    get(name) {
        const config = this.listeners.get(name);
        return config ? config.instance : null;
    }
    
    /**
     * 检查监听器是否活跃
     * @param {string} name - 监听器名称
     */
    isActive(name) {
        const config = this.listeners.get(name);
        return config ? config.isActive : false;
    }
    
    /**
     * 获取所有监听器状态
     */
    getStatus() {
        const status = {};
        
        for (const [name, config] of this.listeners) {
            status[name] = {
                isActive: config.isActive,
                hasInstance: !!config.instance,
                retryCount: config.retryCount,
                lastError: config.lastError?.message || null,
                autoStart: config.autoStart,
                dependencies: config.dependencies
            };
        }
        
        return status;
    }
    
    /**
     * 注册核心监听器
     */
    async registerCoreListeners() {
        // 注册自动保存监听器
        if (window.AutoSaveListener) {
            this.register('autoSave', window.AutoSaveListener, {
                autoStart: true,
                priority: 1
            });
        }
        
        // 注册拖拽上传监听器
        if (window.DragDropUploadListener) {
            this.register('dragDropUpload', window.DragDropUploadListener, {
                autoStart: true,
                priority: 2
            });
        }
        
        // 注册网络状态监听器
        if (window.NetworkStatusListener) {
            this.register('networkStatus', window.NetworkStatusListener, {
                autoStart: true,
                priority: 3
            });
        }
        
        // 注册表单验证监听器
        if (window.FormValidationListener) {
            this.register('formValidation', window.FormValidationListener, {
                autoStart: true,
                priority: 4
            });
        }
        
        console.log('[ListenerRegistry] 核心监听器已注册');
    }
    
    /**
     * 启动自动启动的监听器
     */
    async startAutoStartListeners() {
        // 按优先级排序
        const autoStartListeners = Array.from(this.listeners.entries())
            .filter(([name, config]) => config.autoStart)
            .sort(([, a], [, b]) => a.priority - b.priority);
        
        for (const [name, config] of autoStartListeners) {
            try {
                await this.start(name);
            } catch (error) {
                console.error(`[ListenerRegistry] 自动启动监听器 ${name} 失败:`, error);
            }
        }
    }
    
    /**
     * 检查依赖
     * @param {Object} config - 监听器配置
     */
    async checkDependencies(config) {
        for (const dependency of config.dependencies) {
            if (!this.isActive(dependency)) {
                throw new Error(`依赖的监听器 ${dependency} 未启动`);
            }
        }
    }
    
    /**
     * 处理错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handleError(error, context) {
        const errorInfo = {
            error,
            context,
            timestamp: Date.now()
        };
        
        switch (this.config.errorHandling) {
            case 'throw':
                throw error;
            case 'log':
                console.error(`[ListenerRegistry] ${context}:`, error);
                break;
            case 'silent':
                // 静默处理
                break;
        }
        
        // 触发错误事件
        this.dispatchEvent('error', errorInfo);
    }
    
    /**
     * 分发事件
     * @param {string} eventType - 事件类型
     * @param {Object} detail - 事件详情
     */
    dispatchEvent(eventType, detail) {
        const event = new CustomEvent(`registry:${eventType}`, {
            detail
        });
        this.eventBus.dispatchEvent(event);
        
        // 同时在 document 上分发
        document.dispatchEvent(event);
    }
    
    /**
     * 监听事件
     * @param {string} eventType - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    addEventListener(eventType, handler) {
        this.eventBus.addEventListener(`registry:${eventType}`, handler);
    }
    
    /**
     * 移除事件监听
     * @param {string} eventType - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    removeEventListener(eventType, handler) {
        this.eventBus.removeEventListener(`registry:${eventType}`, handler);
    }
    
    /**
     * 销毁注册中心
     */
    async destroy() {
        // 停止所有活跃的监听器
        const activeListeners = Array.from(this.listeners.entries())
            .filter(([name, config]) => config.isActive);
        
        for (const [name] of activeListeners) {
            try {
                await this.stop(name);
            } catch (error) {
                console.error(`[ListenerRegistry] 停止监听器 ${name} 失败:`, error);
            }
        }
        
        // 清理数据
        this.listeners.clear();
        this.isInitialized = false;
        
        console.log('[ListenerRegistry] 监听器注册中心已销毁');
    }
}

// 导出注册中心
export default ListenerRegistry;

// 创建全局实例
if (typeof window !== 'undefined') {
    window.ListenerRegistry = ListenerRegistry;
    
    // 创建全局注册中心实例
    if (!window.OTA) {
        window.OTA = {};
    }
    
    if (!window.OTA.listenerRegistry) {
        window.OTA.listenerRegistry = new ListenerRegistry();
    }
}
```

## 6. 使用示例

### 6.1 初始化所有监听器

```javascript
// 在主应用中初始化监听器
async function initializeListeners() {
    try {
        // 获取注册中心
        const registry = window.OTA.listenerRegistry;
        
        // 初始化注册中心
        await registry.initialize();
        
        // 手动启动特定监听器（如果需要）
        const formManager = window.OTA?.managers?.form;
        const storageManager = window.OTA?.managers?.storage;
        
        if (formManager && storageManager) {
            await registry.start('autoSave', formManager, storageManager);
        }
        
        console.log('所有监听器已初始化完成');
        
    } catch (error) {
        console.error('监听器初始化失败:', error);
    }
}

// 在页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeListeners);
} else {
    initializeListeners();
}
```

### 6.2 监听器事件处理

```javascript
// 监听自动保存事件
document.addEventListener('autoSave:saved', (event) => {
    console.log('数据已自动保存:', event.detail);
});

// 监听网络状态变化
document.addEventListener('network:offline', (event) => {
    console.log('网络已断开');
    // 显示离线提示
});

document.addEventListener('network:online', (event) => {
    console.log('网络已连接');
    // 隐藏离线提示，恢复操作
});

// 监听表单验证事件
document.addEventListener('validation:formValidated', (event) => {
    const { isValid, errors } = event.detail;
    if (!isValid) {
        console.log('表单验证失败:', errors);
    }
});

// 监听拖拽上传事件
document.addEventListener('upload:filesDropped', (event) => {
    const { files } = event.detail;
    console.log('文件已拖拽:', files);
});
```

### 6.3 动态管理监听器

```javascript
// 获取注册中心
const registry = window.OTA.listenerRegistry;

// 检查监听器状态
console.log('监听器状态:', registry.getStatus());

// 停止特定监听器
await registry.stop('autoSave');

// 重启监听器
await registry.restart('autoSave', formManager, storageManager);

// 获取监听器实例
const networkListener = registry.get('networkStatus');
if (networkListener) {
    const status = networkListener.getStatus();
    console.log('网络状态:', status);
}
```

## 7. 总结

本文档提供了完整的监听器实现代码，包括：

1. **自动保存监听器** - 实现表单数据的自动保存和恢复
2. **拖拽上传监听器** - 处理文件拖拽上传功能
3. **网络状态监听器** - 监控网络连接状态和质量
4. **表单验证监听器** - 提供实时表单验证功能
5. **监听器注册中心** - 统一管理所有监听器的生命周期

所有监听器都遵循统一的设计模式，包含完整的错误处理、事件分发和生命周期管理。通过注册中心可以方便地管理监听器的启动、停止和状态监控。.connection.addEventListener('change', this.handleConnectionChange.bind(this));
        }
        
        // 定期检查网络状态
        this.startPeriodicCheck();
    }
    
    /**
     * 处理上线事件
     */
    handleOnline() {
        console.log('[NetworkStatusListener] 网络已连接');
        
        this.isOnline = true;
        this.lastOnlineTime = Date.now();
        this.reconnectAttempts = 0;
        
        this.updateStatus();
        this.showNotification('网络已连接', 'success');
        
        // 触发上线事件
        this.dispatchNetworkEvent('online', {
            timestamp: this.lastOnlineTime,
            connectionType: this.getConnectionType()
        });
        
        // 尝试恢复中断的操作
        this.resumeInterruptedOperations();
    }
    
    /**
     * 处理离线事件
     */
    handleOffline() {
        console.log('[NetworkStatusListener] 网络已断开');
        
        this.isOnline = false;
        this.lastOfflineTime = Date.now();
        
        this.updateStatus();
        this.showNotification('网络已断开', 'error');
        
        // 触发离线事件
        this.dispatchNetworkEvent('offline', {
            timestamp: this.lastOfflineTime
        });
        
        // 开始重连尝试
        this.startReconnectAttempts();
    }
    
    /**
     * 处理连接类型变化
     */
    handleConnectionChange() {
        const newConnectionType = this.getConnectionType();
        
        if (newConnectionType !== this.connectionType) {
            console.log(`[NetworkStatusListener] 连接类型变化: ${this.connectionType} -> ${newConnectionType}`);
            
            const oldType = this.connectionType;
            this.connectionType = newConnectionType;
            
            this.updateStatus();
            
            // 触发连接类型变化事件
            this.dispatchNetworkEvent('connectionTypeChanged', {
                oldType,
                newType: newConnectionType,
                timestamp: Date.now()
            });
            
            // 如果从慢速连接变为快速连接，通知用户
            if (this.isSlowConnection(oldType) && !this.isSlowConnection(newConnectionType)) {
                this.showNotification('网络连接已改善', 'info');
            } else if (!this.isSlowConnection(oldType) && this.isSlowConnection(newConnectionType)) {
                this.showNotification('网络连接较慢', 'warning');
            }
        }
    }
    
    /**
     * 获取连接类型
     */
    getConnectionType() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            return {
                effectiveType: connection.effectiveType || 'unknown',
                type: connection.type || 'unknown',
                downlink: connection.downlink || 0,
                rtt: connection.rtt || 0,
                saveData: connection.saveData || false
            };
        }
        return {
            effectiveType: 'unknown',
            type: 'unknown',
            downlink: 0,
            rtt: 0,
            saveData: false
        };
    }
    
    /**
     * 判断是否为慢速连接
     * @param {Object} connectionType - 连接类型
     */
    isSlowConnection(connectionType) {
        if (!connectionType) connectionType = this.connectionType;
        
        return connectionType.effectiveType === 'slow-2g' || 
               connectionType.effectiveType === '2g' ||
               connectionType.downlink < 1;
    }
    
    /**
     * 开始定期检查
     */
    startPeriodicCheck() {
        // 每30秒检查一次网络状态
        setInterval(() => {
            this.checkNetworkStatus();
        }, 30000);
    }
    
    /**
     * 检查网络状态
     */
    async checkNetworkStatus() {
        try {
            // 尝试发送一个小的请求来验证网络连接
            const response = await fetch('/api/ping', {
                method: 'HEAD',
                cache: 'no-cache',
                timeout: 5000
            });
            
            const isActuallyOnline = response.ok;
            
            // 如果浏览器认为在线但实际请求失败
            if (navigator.onLine && !isActuallyOnline) {
                console.warn('[NetworkStatusListener] 浏览器显示在线但网络请求失败');
                this.handleNetworkIssue();
            }
            
        } catch (error) {
            if (navigator.onLine) {
                console.warn('[NetworkStatusListener] 网络检查失败:', error.message);
                this.handleNetworkIssue();
            }
        }
    }
    
    /**
     * 处理网络问题
     */
    handleNetworkIssue() {
        this.showNotification('网络连接不稳定', 'warning');
        
        // 触发网络问题事件
        this.dispatchNetworkEvent('networkIssue', {
            timestamp: Date.now(),
            message: '网络连接不稳定'
        });
    }
    
    /**
     * 开始重连尝试
     */
    startReconnectAttempts() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('[NetworkStatusListener] 已达到最大重连次数');
            return;
        }
        
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts); // 指数退避
        
        setTimeout(() => {
            this.attemptReconnect();
        }, delay);
    }
    
    /**
     * 尝试重连
     */
    async attemptReconnect() {
        this.reconnectAttempts++;
        
        console.log(`[NetworkStatusListener] 重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        
        try {
            // 尝试发送请求检查连接
            const response = await fetch('/api/ping', {
                method: 'HEAD',
                cache: 'no-cache',
                timeout: 3000
            });
            
            if (response.ok) {
                console.log('[NetworkStatusListener] 重连成功');
                this.handleOnline();
                return;
            }
        } catch (error) {
            console.log(`[NetworkStatusListener] 重连失败: ${error.message}`);
        }
        
        // 如果还没达到最大次数，继续尝试
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.startReconnectAttempts();
        } else {
            this.showNotification('无法重新连接网络，请检查网络设置', 'error');
        }
    }
    
    /**
     * 恢复中断的操作
     */
    resumeInterruptedOperations() {
        // 触发恢复事件，让其他组件知道可以重试失败的操作
        this.dispatchNetworkEvent('resumeOperations', {
            timestamp: Date.now()
        });
        
        // 如果有自动保存功能，触发保存
        if (window.OTA?.listenerRegistry?.get('autoSave')) {
            window.OTA.listenerRegistry.dispatchEvent('networkReconnected');
        }
    }
    
    /**
     * 创建状态指示器
     */
    createStatusIndicator() {
        this.statusIndicator = document.createElement('div');
        this.statusIndicator.id = 'networkStatusIndicator';
        this.statusIndicator.className = 'fixed bottom-4 left-4 px-3 py-1 rounded text-sm z-50 transition-all duration-300';
        
        document.body.appendChild(this.statusIndicator);
    }
    
    /**
     * 更新状态显示
     */
    updateStatus() {
        if (!this.statusIndicator) return;
        
        if (this.isOnline) {
            this.statusIndicator.className = 'fixed bottom-4 left-4 px-3 py-1 rounded text-sm z-50 transition-all duration-300 bg-green-500 text-white';
            
            let statusText = '🟢 在线';
            
            if (this.connectionType.effectiveType !== 'unknown') {
                statusText += ` (${this.connectionType.effectiveType.toUpperCase()})`;
            }
            
            if (this.isSlowConnection()) {
                statusText += ' - 连接较慢';
                this.statusIndicator.className = 'fixed bottom-4 left-4 px-3 py-1 rounded text-sm z-50 transition-all duration-300 bg-yellow-500 text-white';
            }
            
            this.statusIndicator.textContent = statusText;
            
            // 3秒后隐藏在线状态
            setTimeout(() => {
                if (this.isOnline) {
                    this.statusIndicator.style.opacity = '0.3';
                }
            }, 3000);
            
        } else {
            this.statusIndicator.className = 'fixed bottom-4 left-4 px-3 py-1 rounded text-sm z-50 transition-all duration-300 bg-red-500 text-white';
            this.statusIndicator.textContent = '🔴 离线';
            this.statusIndicator.style.opacity = '1';
        }
    }
    
    /**
     * 显示通知
     * @param {string} message - 消息
     * @param {string} type - 类型
     */
    showNotification(message, type) {
        // 使用UI管理器显示通知
        if (window.OTA?.managers?.ui?.showQuickToast) {
            window.OTA.managers.ui.showQuickToast(message, type);
        } else {
            // 创建简单的通知
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-4 py-2 rounded text-white z-50 ${this.getNotificationClass(type)}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    }
    
    /**
     * 获取通知样式类
     * @param {string} type - 类型
     */
    getNotificationClass(type) {
        const classes = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };
        return classes[type] || classes.info;
    }
    
    /**
     * 分发网络事件
     * @param {string} eventType - 事件类型
     * @param {Object} detail - 事件详情
     */
    dispatchNetworkEvent(eventType, detail) {
        const event = new CustomEvent(`network:${eventType}`, {
            detail,
            bubbles: true
        });
        document.dispatchEvent(event);
    }
    
    /**
     * 获取网络状态信息
     */
    getStatus() {
        return {
            isOnline: this.isOnline,
            connectionType: this.connectionType,
            lastOnlineTime: this.lastOnlineTime,
            lastOfflineTime: this.lastOfflineTime,
            reconnectAttempts: this.reconnectAttempts,
            isSlowConnection: this.isSlowConnection()
        };
    }
    
    /**
     * 手动检查网络
     */
    async manualCheck() {
        console.log('[NetworkStatusListener] 手动检查网络状态');
        await this.checkNetworkStatus();
        return this.getStatus();
    }
    
    /**
     * 销毁监听器
     */
    destroy() {
        // 移除事件监听器
        window.removeEventListener('online', this.handleOnline);
        window.removeEventListener('offline', this.handleOffline);
        
        if ('connection' in navigator) {
            navigator