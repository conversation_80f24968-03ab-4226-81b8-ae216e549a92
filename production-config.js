/**
 * 生产环境配置 - Linus重构版
 * 
 * 集中管理生产环境的所有配置
 * "配置即代码" - DevOps最佳实践
 */

'use strict';

class ProductionConfig {
    constructor() {
        this.environment = this.detectEnvironment();
        this.config = this.loadConfig();
        
        this.init();
    }

    detectEnvironment() {
        const hostname = window.location.hostname;
        
        if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('192.168.')) {
            return 'development';
        } else if (hostname.includes('staging') || hostname.includes('test')) {
            return 'staging';
        } else {
            return 'production';
        }
    }

    loadConfig() {
        const baseConfig = {
            // 应用信息
            app: {
                name: 'OTA订单处理系统',
                version: '2.4.2-linus-refactor',
                build: Date.now(),
                environment: this.environment
            },

            // API配置
            api: {
                baseURL: this.getAPIBaseURL(),
                timeout: 30000,
                retries: 3,
                retryDelay: 1000
            },

            // Gemini配置
            gemini: {
                endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent',
                timeout: 45000,
                maxTokens: 4096,
                temperature: 0.1
            },

            // 性能监控
            monitoring: {
                enabled: this.environment === 'production',
                sampleRate: this.environment === 'production' ? 0.1 : 1.0,
                reportInterval: 60000, // 1分钟
                endpoints: {
                    metrics: '/api/monitoring/metrics',
                    errors: '/api/monitoring/errors',
                    vitals: '/api/monitoring/vitals'
                }
            },

            // 错误监控
            errorTracking: {
                enabled: true,
                maxErrors: this.environment === 'production' ? 100 : 500,
                reportImmediately: this.environment === 'production',
                alertThresholds: {
                    errorRate: 0.05,
                    criticalErrors: 3,
                    timeWindow: 300000 // 5分钟
                }
            },

            // 缓存配置
            cache: {
                enabled: true,
                version: 'v2.4.2',
                strategies: {
                    static: 'cache-first',
                    api: 'network-first',
                    images: 'cache-first'
                },
                ttl: {
                    static: 7 * 24 * 60 * 60 * 1000, // 7天
                    api: 60 * 60 * 1000, // 1小时
                    images: 30 * 24 * 60 * 60 * 1000 // 30天
                }
            },

            // 安全配置
            security: {
                enableCSP: this.environment === 'production',
                enableHSTS: this.environment === 'production',
                apiKeyRotationInterval: 24 * 60 * 60 * 1000, // 24小时
                sessionTimeout: 4 * 60 * 60 * 1000, // 4小时
                maxLoginAttempts: 5
            },

            // 功能开关
            features: {
                imageUpload: true,
                multiOrderProcessing: true,
                offlineMode: true,
                darkMode: true,
                analytics: this.environment === 'production',
                debugMode: this.environment !== 'production'
            },

            // CDN配置
            cdn: {
                enabled: this.environment === 'production',
                baseURL: 'https://cdn.gomyhire.com',
                assets: ['js', 'css', 'images'],
                version: '2.4.2'
            },

            // PWA配置
            pwa: {
                enabled: true,
                serviceWorker: '/sw.js',
                manifest: '/manifest.json',
                updatePrompt: true,
                installPrompt: this.environment === 'production'
            }
        };

        // 环境特定配置覆盖
        return this.applyEnvironmentOverrides(baseConfig);
    }

    getAPIBaseURL() {
        switch (this.environment) {
            case 'development':
                return 'http://localhost:8000/api';
            case 'staging':
                return 'https://staging-api.gomyhire.com/api';
            case 'production':
            default:
                return 'https://gomyhire.com.my/api';
        }
    }

    applyEnvironmentOverrides(config) {
        const overrides = {
            development: {
                monitoring: {
                    enabled: true,
                    sampleRate: 1.0,
                    reportInterval: 10000 // 10秒，便于调试
                },
                errorTracking: {
                    maxErrors: 1000,
                    reportImmediately: false
                },
                features: {
                    debugMode: true,
                    analytics: false
                }
            },

            staging: {
                monitoring: {
                    enabled: true,
                    sampleRate: 0.5,
                    reportInterval: 30000 // 30秒
                },
                security: {
                    enableCSP: false, // 便于调试
                    sessionTimeout: 8 * 60 * 60 * 1000 // 8小时
                }
            },

            production: {
                features: {
                    debugMode: false
                },
                security: {
                    enableCSP: true,
                    enableHSTS: true
                },
                cdn: {
                    enabled: true
                }
            }
        };

        const environmentOverride = overrides[this.environment] || {};
        return this.deepMerge(config, environmentOverride);
    }

    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(target[key] || {}, source[key]);
                } else {
                    result[key] = source[key];
                }
            }
        }
        
        return result;
    }

    init() {
        // 应用配置到全局对象
        if (window.ota) {
            window.ota.config = this.config;
        }

        // 设置全局配置
        window.PRODUCTION_CONFIG = this.config;

        // 初始化安全策略
        this.initSecurity();

        // 初始化性能监控
        this.initMonitoring();

        // 初始化PWA
        this.initPWA();

        // 初始化CDN
        this.initCDN();

        console.log(`🚀 生产环境配置已加载 [${this.environment}]`, this.config);
    }

    initSecurity() {
        const { security } = this.config;

        if (security.enableCSP) {
            this.setContentSecurityPolicy();
        }

        if (security.enableHSTS) {
            this.setSecurityHeaders();
        }

        // 会话超时检查
        this.setupSessionTimeout();
    }

    setContentSecurityPolicy() {
        const csp = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' https://generativelanguage.googleapis.com",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "connect-src 'self' https://gomyhire.com.my https://generativelanguage.googleapis.com",
            "font-src 'self'",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ].join('; ');

        const meta = document.createElement('meta');
        meta.httpEquiv = 'Content-Security-Policy';
        meta.content = csp;
        document.head.appendChild(meta);
    }

    setSecurityHeaders() {
        // 这些通常在服务器端设置，这里只是记录
        console.log('🔒 安全头配置：', {
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin'
        });
    }

    setupSessionTimeout() {
        const { sessionTimeout } = this.config.security;
        
        let lastActivity = Date.now();
        
        // 监听用户活动
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, () => {
                lastActivity = Date.now();
            }, { passive: true });
        });

        // 定期检查会话超时
        setInterval(() => {
            if (Date.now() - lastActivity > sessionTimeout) {
                this.handleSessionTimeout();
            }
        }, 60000); // 每分钟检查一次
    }

    handleSessionTimeout() {
        console.warn('🕐 会话超时，自动登出');
        
        // 清除认证信息
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_email');
        
        // 重定向到登录页
        if (window.location.pathname !== '/') {
            window.location.href = '/';
        }
    }

    initMonitoring() {
        const { monitoring, errorTracking } = this.config;

        if (monitoring.enabled) {
            // 延迟加载性能监控，避免影响启动性能
            setTimeout(() => {
                import('./performance-monitor.js').then(module => {
                    window.performanceMonitor = new module.default(monitoring);
                });
            }, 2000);
        }

        if (errorTracking.enabled) {
            // 立即加载错误监控
            import('./error-monitor.js').then(module => {
                window.errorMonitor = new module.default(errorTracking);
            });
        }
    }

    initPWA() {
        const { pwa } = this.config;

        if (!pwa.enabled) return;

        // 注册Service Worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register(pwa.serviceWorker)
                .then(registration => {
                    console.log('✅ Service Worker 注册成功');
                    
                    // 检查更新
                    registration.addEventListener('updatefound', () => {
                        this.handleServiceWorkerUpdate(registration);
                    });
                })
                .catch(error => {
                    console.error('❌ Service Worker 注册失败:', error);
                });
        }

        // 安装提示
        if (pwa.installPrompt) {
            this.setupInstallPrompt();
        }
    }

    handleServiceWorkerUpdate(registration) {
        const newWorker = registration.installing;
        
        newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                if (this.config.pwa.updatePrompt) {
                    this.showUpdatePrompt();
                }
            }
        });
    }

    showUpdatePrompt() {
        const updateBanner = document.createElement('div');
        updateBanner.id = 'update-banner';
        updateBanner.innerHTML = `
            <div style="background: #2196F3; color: white; padding: 10px; text-align: center; position: fixed; top: 0; left: 0; right: 0; z-index: 10000;">
                🔄 新版本可用！
                <button onclick="window.location.reload()" style="margin-left: 10px; padding: 5px 10px; background: white; color: #2196F3; border: none; border-radius: 3px;">更新</button>
                <button onclick="this.parentNode.parentNode.remove()" style="margin-left: 5px; padding: 5px 10px; background: rgba(255,255,255,0.2); color: white; border: none; border-radius: 3px;">稍后</button>
            </div>
        `;
        document.body.appendChild(updateBanner);
    }

    setupInstallPrompt() {
        let deferredPrompt;

        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // 显示安装按钮
            this.showInstallButton(deferredPrompt);
        });
    }

    showInstallButton(deferredPrompt) {
        const installButton = document.createElement('button');
        installButton.textContent = '📱 安装应用';
        installButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        `;

        installButton.addEventListener('click', () => {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    console.log('✅ 用户接受了安装提示');
                } else {
                    console.log('❌ 用户拒绝了安装提示');
                }
                deferredPrompt = null;
                installButton.remove();
            });
        });

        document.body.appendChild(installButton);

        // 30秒后自动隐藏
        setTimeout(() => {
            if (installButton.parentNode) {
                installButton.remove();
            }
        }, 30000);
    }

    initCDN() {
        const { cdn } = this.config;

        if (!cdn.enabled) return;

        // 预加载CDN资源
        this.preloadCDNAssets();
        
        // 设置资源URL重写
        this.setupResourceRewriting();
    }

    preloadCDNAssets() {
        const { cdn } = this.config;
        
        // 预加载关键CSS和JS资源
        const criticalAssets = [
            `${cdn.baseURL}/css/main.css?v=${cdn.version}`,
            `${cdn.baseURL}/js/core.js?v=${cdn.version}`
        ];

        criticalAssets.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = url;
            link.as = url.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    }

    setupResourceRewriting() {
        // 重写资源URL以使用CDN
        // 这通常在构建时处理，这里只是示例
        console.log('🌐 CDN资源重写已配置');
    }

    // 公共API
    get(path) {
        return this.getNestedValue(this.config, path);
    }

    set(path, value) {
        this.setNestedValue(this.config, path, value);
    }

    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!current[key]) current[key] = {};
            return current[key];
        }, obj);
        target[lastKey] = value;
    }

    getEnvironmentInfo() {
        return {
            environment: this.environment,
            version: this.config.app.version,
            build: this.config.app.build,
            features: this.config.features
        };
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.ProductionConfig = ProductionConfig;
    window.productionConfig = new ProductionConfig();
}

export default ProductionConfig;