/**
 * 负载测试工具 - Linus重构版
 * 
 * 模拟真实用户行为，测试系统在高负载下的表现
 * "在生产环境中失败，不如在测试中失败" - DevOps原则
 */

'use strict';

class LoadTester {
    constructor(config = {}) {
        this.config = {
            baseURL: window.location.origin,
            concurrency: 10, // 并发用户数
            duration: 60000, // 测试持续时间(毫秒)
            rampUpTime: 10000, // 加载时间
            testScenarios: ['basic_flow', 'api_stress', 'multi_order', 'image_upload'],
            thresholds: {
                responseTime: 2000, // 2秒响应时间阈值
                errorRate: 0.05, // 5%错误率阈值
                throughput: 10 // 最小吞吐量(req/s)
            },
            ...config
        };

        this.results = {
            startTime: null,
            endTime: null,
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            responseTimes: [],
            errors: [],
            scenarios: {}
        };

        this.workers = [];
        this.isRunning = false;
    }

    async runLoadTest() {
        console.log('🚀 开始负载测试...');
        console.log('配置:', this.config);

        this.results.startTime = Date.now();
        this.isRunning = true;

        // 创建测试工作器
        await this.createWorkers();

        // 等待测试完成
        await this.waitForCompletion();

        // 收集结果
        this.results.endTime = Date.now();
        this.isRunning = false;

        // 生成报告
        const report = this.generateReport();
        console.log('📊 负载测试完成:', report);

        return report;
    }

    async createWorkers() {
        const { concurrency, rampUpTime } = this.config;
        const rampUpInterval = rampUpTime / concurrency;

        for (let i = 0; i < concurrency; i++) {
            // 延迟启动，模拟真实的用户行为
            setTimeout(() => {
                const worker = new LoadTestWorker(i, this.config, this.onWorkerResult.bind(this));
                this.workers.push(worker);
                worker.start();
            }, i * rampUpInterval);
        }
    }

    onWorkerResult(workerId, result) {
        this.results.totalRequests++;
        
        if (result.success) {
            this.results.successfulRequests++;
            this.results.responseTimes.push(result.responseTime);
        } else {
            this.results.failedRequests++;
            this.results.errors.push({
                workerId,
                error: result.error,
                timestamp: Date.now()
            });
        }

        // 记录场景结果
        if (!this.results.scenarios[result.scenario]) {
            this.results.scenarios[result.scenario] = {
                total: 0,
                successful: 0,
                failed: 0,
                responseTimes: []
            };
        }

        const scenarioResult = this.results.scenarios[result.scenario];
        scenarioResult.total++;
        
        if (result.success) {
            scenarioResult.successful++;
            scenarioResult.responseTimes.push(result.responseTime);
        } else {
            scenarioResult.failed++;
        }
    }

    async waitForCompletion() {
        return new Promise((resolve) => {
            setTimeout(() => {
                this.stopAllWorkers();
                resolve();
            }, this.config.duration);
        });
    }

    stopAllWorkers() {
        this.workers.forEach(worker => worker.stop());
        this.workers = [];
    }

    generateReport() {
        const duration = this.results.endTime - this.results.startTime;
        const throughput = (this.results.totalRequests / duration) * 1000; // req/s
        const errorRate = this.results.failedRequests / this.results.totalRequests;
        
        const responseTimes = this.results.responseTimes.sort((a, b) => a - b);
        const percentiles = {
            p50: this.getPercentile(responseTimes, 50),
            p95: this.getPercentile(responseTimes, 95),
            p99: this.getPercentile(responseTimes, 99)
        };

        const report = {
            summary: {
                duration: duration,
                totalRequests: this.results.totalRequests,
                successfulRequests: this.results.successfulRequests,
                failedRequests: this.results.failedRequests,
                throughput: throughput,
                errorRate: errorRate,
                averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length || 0
            },
            percentiles,
            scenarios: this.generateScenarioReport(),
            thresholds: this.checkThresholds(throughput, errorRate, percentiles.p95),
            errors: this.results.errors.slice(0, 10) // 前10个错误
        };

        return report;
    }

    generateScenarioReport() {
        const scenarioReport = {};
        
        for (const [scenario, data] of Object.entries(this.results.scenarios)) {
            const responseTimes = data.responseTimes.sort((a, b) => a - b);
            scenarioReport[scenario] = {
                total: data.total,
                successful: data.successful,
                failed: data.failed,
                successRate: data.successful / data.total,
                averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length || 0,
                p95: this.getPercentile(responseTimes, 95)
            };
        }
        
        return scenarioReport;
    }

    checkThresholds(throughput, errorRate, p95ResponseTime) {
        const { thresholds } = this.config;
        
        return {
            throughput: {
                value: throughput,
                threshold: thresholds.throughput,
                passed: throughput >= thresholds.throughput
            },
            errorRate: {
                value: errorRate,
                threshold: thresholds.errorRate,
                passed: errorRate <= thresholds.errorRate
            },
            responseTime: {
                value: p95ResponseTime,
                threshold: thresholds.responseTime,
                passed: p95ResponseTime <= thresholds.responseTime
            }
        };
    }

    getPercentile(sortedArray, percentile) {
        if (sortedArray.length === 0) return 0;
        const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
        return sortedArray[Math.max(0, index)];
    }
}

class LoadTestWorker {
    constructor(id, config, resultCallback) {
        this.id = id;
        this.config = config;
        this.resultCallback = resultCallback;
        this.isRunning = false;
        this.requestCount = 0;
    }

    start() {
        this.isRunning = true;
        this.runScenarios();
    }

    stop() {
        this.isRunning = false;
    }

    async runScenarios() {
        while (this.isRunning) {
            const scenario = this.selectScenario();
            await this.executeScenario(scenario);
            
            // 随机延迟，模拟真实用户思考时间
            await this.randomDelay(500, 2000);
        }
    }

    selectScenario() {
        const scenarios = this.config.testScenarios;
        return scenarios[Math.floor(Math.random() * scenarios.length)];
    }

    async executeScenario(scenario) {
        const startTime = Date.now();
        
        try {
            switch (scenario) {
                case 'basic_flow':
                    await this.basicFlowScenario();
                    break;
                case 'api_stress':
                    await this.apiStressScenario();
                    break;
                case 'multi_order':
                    await this.multiOrderScenario();
                    break;
                case 'image_upload':
                    await this.imageUploadScenario();
                    break;
                default:
                    await this.basicFlowScenario();
            }
            
            const responseTime = Date.now() - startTime;
            this.resultCallback(this.id, {
                scenario,
                success: true,
                responseTime,
                timestamp: Date.now()
            });
            
        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.resultCallback(this.id, {
                scenario,
                success: false,
                error: error.message,
                responseTime,
                timestamp: Date.now()
            });
        }
    }

    // 基础流程场景：模拟用户正常使用流程
    async basicFlowScenario() {
        // 1. 加载页面
        await this.makeRequest('/', 'GET');
        
        // 2. 模拟登录
        await this.simulateLogin();
        
        // 3. 创建订单
        await this.createTestOrder();
        
        // 4. 查看历史
        await this.makeRequest('/api/v1/order-jobs?limit=10', 'GET');
    }

    // API压力场景：快速连续API调用
    async apiStressScenario() {
        const apiEndpoints = [
            '/api/v1/sub-categories',
            '/api/v1/car-types',
            '/api/v1/driving-regions'
        ];
        
        // 并发调用多个API
        const promises = apiEndpoints.map(endpoint => 
            this.makeRequest(endpoint, 'GET')
        );
        
        await Promise.all(promises);
    }

    // 多订单场景：批量处理
    async multiOrderScenario() {
        const orders = this.generateTestOrders(3);
        
        for (const order of orders) {
            await this.makeRequest('/api/v1/order-jobs', 'POST', order);
            await this.randomDelay(100, 500); // 订单间延迟
        }
    }

    // 图片上传场景：模拟文件上传
    async imageUploadScenario() {
        // 创建模拟图片数据
        const mockImageData = this.createMockImageData();
        
        await this.makeRequest('/api/v1/upload', 'POST', mockImageData);
    }

    async simulateLogin() {
        const loginData = {
            email: `test${this.id}@example.com`,
            password: 'testpassword'
        };
        
        return this.makeRequest('/api/login', 'POST', loginData);
    }

    async createTestOrder() {
        const orderData = {
            customer_name: `测试客户${this.id}-${this.requestCount++}`,
            customer_contact: `1380013800${this.id}`,
            pickup: '香格里拉酒店',
            destination: 'KLIA机场',
            date: '2025-08-16',
            time: '14:00',
            passenger_number: 2
        };
        
        return this.makeRequest('/api/v1/order-jobs', 'POST', orderData);
    }

    generateTestOrders(count) {
        const orders = [];
        const hotels = ['香格里拉酒店', '希尔顿酒店', '万豪酒店'];
        const destinations = ['KLIA机场', 'LCCT机场', 'KL Central'];
        
        for (let i = 0; i < count; i++) {
            orders.push({
                customer_name: `批量客户${this.id}-${i}`,
                customer_contact: `1380013800${this.id}`,
                pickup: hotels[Math.floor(Math.random() * hotels.length)],
                destination: destinations[Math.floor(Math.random() * destinations.length)],
                date: '2025-08-16',
                time: `${10 + i}:00`,
                passenger_number: Math.floor(Math.random() * 4) + 1
            });
        }
        
        return orders;
    }

    createMockImageData() {
        // 创建1KB的模拟图片数据
        const size = 1024;
        const buffer = new ArrayBuffer(size);
        const view = new Uint8Array(buffer);
        
        for (let i = 0; i < size; i++) {
            view[i] = Math.floor(Math.random() * 256);
        }
        
        return {
            image: Array.from(view),
            filename: `test-image-${this.id}.jpg`,
            type: 'image/jpeg'
        };
    }

    async makeRequest(url, method = 'GET', data = null) {
        const fullURL = url.startsWith('/') ? `${this.config.baseURL}${url}` : url;
        
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': `LoadTest-Worker-${this.id}`
            }
        };
        
        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(fullURL, options);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return response.json();
    }

    async randomDelay(min, max) {
        const delay = Math.floor(Math.random() * (max - min + 1)) + min;
        return new Promise(resolve => setTimeout(resolve, delay));
    }
}

// 预定义测试配置
const TEST_PROFILES = {
    // 轻量测试
    light: {
        concurrency: 5,
        duration: 30000,
        rampUpTime: 5000
    },
    
    // 中等测试
    medium: {
        concurrency: 20,
        duration: 120000,
        rampUpTime: 20000
    },
    
    // 重负载测试
    heavy: {
        concurrency: 50,
        duration: 300000,
        rampUpTime: 60000
    },
    
    // 压力测试
    stress: {
        concurrency: 100,
        duration: 600000,
        rampUpTime: 120000,
        thresholds: {
            responseTime: 5000,
            errorRate: 0.1,
            throughput: 5
        }
    }
};

// 便捷函数
async function runLoadTest(profile = 'light') {
    const config = TEST_PROFILES[profile];
    if (!config) {
        throw new Error(`Unknown test profile: ${profile}`);
    }
    
    const tester = new LoadTester(config);
    return tester.runLoadTest();
}

// 自动化测试套件
async function runTestSuite() {
    console.log('🧪 开始自动化测试套件...');
    
    const results = {};
    
    for (const [profile, config] of Object.entries(TEST_PROFILES)) {
        console.log(`\n📊 运行 ${profile} 负载测试...`);
        
        try {
            const result = await runLoadTest(profile);
            results[profile] = result;
            
            // 检查是否通过阈值
            const passed = Object.values(result.thresholds).every(t => t.passed);
            console.log(`${profile} 测试: ${passed ? '✅ 通过' : '❌ 失败'}`);
            
        } catch (error) {
            console.error(`${profile} 测试失败:`, error);
            results[profile] = { error: error.message };
        }
        
        // 测试间隔
        await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    console.log('\n📈 测试套件完成:', results);
    return results;
}

// 导出
if (typeof window !== 'undefined') {
    window.LoadTester = LoadTester;
    window.runLoadTest = runLoadTest;
    window.runTestSuite = runTestSuite;
    window.TEST_PROFILES = TEST_PROFILES;
}

export { LoadTester, runLoadTest, runTestSuite, TEST_PROFILES };