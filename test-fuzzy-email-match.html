<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模糊邮箱匹配测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .failure {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-details {
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🧪 模糊邮箱匹配功能测试</h1>
    <p>此测试验证 <code>fuzzyEmailMatch</code> 函数是否能正确处理带前缀的邮箱地址匹配。</p>
    
    <div id="test-results"></div>

    <script>
        // 模拟 fuzzyEmailMatch 函数（从 api-service.js 复制）
        function fuzzyEmailMatch(loginEmail, storedEmail) {
            if (!loginEmail || !storedEmail) return false;
            
            // 1. 精确匹配（优先级最高）
            if (loginEmail.toLowerCase() === storedEmail.toLowerCase()) {
                return true;
            }
            
            /**
             * 提取核心邮箱部分 - 移除常见的前缀
             * 例如：'SMW <EMAIL>' → '<EMAIL>'
             */
            const extractCoreEmail = (email) => {
                const trimmed = email.trim();
                const parts = trimmed.split('@');
                if (parts.length !== 2) return trimmed.toLowerCase();
                
                const userPart = parts[0].trim();
                const domain = parts[1].trim();
                
                // 移除常见的业务前缀（不区分大小写）
                const prefixPatterns = [
                    /^SMW\s+/i,       // SMW Wendy → Wendy
                    /^GMH\s+/i,       // GMH Admin → Admin  
                    /^OP\s+/i,        // OP Annie → Annie
                    /^CS\s*[Tt]eam\s+/i // CSteam → (去掉CSteam前缀)
                ];
                
                let cleanUser = userPart;
                for (const pattern of prefixPatterns) {
                    cleanUser = cleanUser.replace(pattern, '');
                }
                
                // 额外清理：移除多余空格并转为小写
                cleanUser = cleanUser.trim().toLowerCase();
                
                return `${cleanUser}@${domain.toLowerCase()}`;
            };
            
            // 2. 核心邮箱匹配
            const coreLoginEmail = extractCoreEmail(loginEmail);
            const coreStoredEmail = extractCoreEmail(storedEmail);
            
            console.log('📧 模糊邮箱匹配尝试', {
                original: { loginEmail, storedEmail },
                extracted: { coreLoginEmail, coreStoredEmail },
                match: coreLoginEmail === coreStoredEmail
            });
            
            return coreLoginEmail === coreStoredEmail;
        }

        // 测试用例
        const testCases = [
            {
                name: "Wendy 邮箱匹配测试（主要问题）",
                login: "<EMAIL>",
                stored: "SMW <EMAIL>",
                expected: true,
                priority: "high"
            },
            {
                name: "JCY 邮箱精确匹配（应该仍然工作）",
                login: "<EMAIL>", 
                stored: "<EMAIL>",
                expected: true,
                priority: "high"
            },
            {
                name: "GMH 前缀匹配测试",
                login: "<EMAIL>",
                stored: "GMH <EMAIL>",
                expected: true,
                priority: "medium"
            },
            {
                name: "OP 前缀匹配测试", 
                login: "<EMAIL>",
                stored: "OP <EMAIL>",
                expected: true,
                priority: "medium"
            },
            {
                name: "CSteam 前缀匹配测试",
                login: "<EMAIL>", 
                stored: "CSteam <EMAIL>",
                expected: true,
                priority: "low"
            },
            {
                name: "不匹配的邮箱测试",
                login: "<EMAIL>",
                stored: "<EMAIL>", 
                expected: false,
                priority: "medium"
            },
            {
                name: "大小写不敏感测试",
                login: "<EMAIL>",
                stored: "<EMAIL>",
                expected: true,
                priority: "low"
            },
            {
                name: "多个空格处理测试",
                login: "<EMAIL>",
                stored: "SMW   User  @gomyhire.com",
                expected: true,
                priority: "low"
            }
        ];

        // 执行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let passedTests = 0;
            let totalTests = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = fuzzyEmailMatch(testCase.login, testCase.stored);
                const passed = result === testCase.expected;
                
                if (passed) passedTests++;

                const testDiv = document.createElement('div');
                testDiv.className = `test-case ${passed ? 'success' : 'failure'}`;
                
                const priorityIcon = testCase.priority === 'high' ? '🔥' : 
                                   testCase.priority === 'medium' ? '⚡' : '📝';
                
                testDiv.innerHTML = `
                    <h3>${priorityIcon} ${testCase.name} ${passed ? '✅' : '❌'}</h3>
                    <p><strong>登录邮箱:</strong> <code>${testCase.login}</code></p>
                    <p><strong>存储邮箱:</strong> <code>${testCase.stored}</code></p>
                    <p><strong>期望结果:</strong> ${testCase.expected ? '匹配' : '不匹配'}</p>
                    <p><strong>实际结果:</strong> ${result ? '匹配' : '不匹配'}</p>
                    <div class="test-details">
                        优先级: ${testCase.priority} | 状态: ${passed ? 'PASS' : 'FAIL'}
                    </div>
                `;
                
                resultsDiv.appendChild(testDiv);
            });

            // 添加总结
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-case ${passedTests === totalTests ? 'success' : 'failure'}`;
            summaryDiv.innerHTML = `
                <h2>📊 测试总结</h2>
                <p><strong>通过测试:</strong> ${passedTests}/${totalTests}</p>
                <p><strong>成功率:</strong> ${Math.round((passedTests/totalTests) * 100)}%</p>
                ${passedTests === totalTests ? 
                    '<p>🎉 所有测试通过！模糊邮箱匹配功能正常工作。</p>' : 
                    '<p>⚠️ 部分测试失败，需要检查实现。</p>'
                }
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // 页面加载后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>