# 项目静态数据总览 (2025-08-19)

> 目的：集中列出前端仓库内以“硬编码/内嵌”形式存在的业务静态数据（非动态API实时获取部分），包含文件位置、数据类别、用途、关键字段结构，及维护建议。便于后续抽离、同步或校验。
>
> 范围说明：
>
> - 包含：配置对象、数组（用户、渠道、车型、区域、语言、特性开关、策略参数等）。
>
> - 不含：纯函数逻辑、运行时缓存、浏览器本地存储生成的数据。
>
> 分类索引：
1. 用户与权限静态数据
2. OTA 渠道静态数据
3. 业务字典（子分类 / 车型 / 行驶区域 / 语言）
4. 酒店数据库系统
5. 系统配置与构建信息
6. 脚本加载与多语言配置
7. 环境与特性配置
8. 废弃/兼容静态数据（保留原因）
9. 其他潜在静态数据文件（占位）
10. 统一抽离建议
>
> 快速定位：在 VSCode 搜索关键词，如 `COMPLETE_CHANNEL_LIST`、`staticData = {`、`features:`。

---

## 📋 核心业务数据速览 (集中查看区)

### 🎯 渠道列表数据
**文件**: `js/config/user-permissions-config.js` **[行 84-198]**

**完整渠道列表** (`COMPLETE_CHANNEL_LIST`):
```js
// 主要渠道 (按使用频次排序)
'Agoda', 'Booking.com', 'Expedia', 'Hotels.com', 'Airbnb', 'Trip.com', 'Traveloka', 'Klook', 'KKday', 'GetYourGuide',

// 平台特定渠道
'Grab', 'Foodpanda', 'Shopee', 'Lazada', 'ZALORA', 'Sephora', 'Guardian', 'Watsons',

// 企业/机构渠道
'JR Coach Credit', 'JR Coach Cash', 'UCSI - Cheras', 'UCSI - Port Dickson', 'Student Travel',

// 专业服务渠道  
'JR COACH SERVICES - C1', 'JR COACH SERVICES - HTP - C1', 'JR COACH SERVICES - GTV - C1', 
'JR COACH SERVICES - JRV - C1', 'JR COACH SERVICES - WYNN - C1', 'JR COACH SERVICES - EJH - C1',

// 测试/开发渠道
'final_deployment_test', 'success_test', 'Klook test', 'Heycar test', 'KKDAY SGD',

// 特殊/其他
'Refund', 'Other', 'Sativa - ID - Cynthia HTP', 'Kai - TC', 'JR COACH SERVICES - HTC - C1'
```
**总数**: 114个渠道

### 👥 用户ID列表
**文件**: `js/config/user-permissions-config.js` **[行 69-82]**

**完整用户列表** (`COMPLETE_USER_LIST`):
```js
// 系统管理员
{ id: 1, name: 'Admin User', email: '<EMAIL>', phone: '+60123456789', role_id: 1 },

// 运营团队
{ id: 2, name: 'Operations Manager', email: '<EMAIL>', phone: '+60123456790', role_id: 2 },
{ id: 3, name: 'Customer Service', email: '<EMAIL>', phone: '+60123456791', role_id: 3 },

// 合作伙伴账户
{ id: 2666, name: 'JR Coach Services', email: '<EMAIL>', phone: '+60123456792', role_id: 4 },
{ id: 2446, name: 'UCSI University', email: '<EMAIL>', phone: '+60123456793', role_id: 4 },

// 开发/测试账户
{ id: 9999, name: 'Test User', email: '<EMAIL>', phone: '+60123456794', role_id: 5 },
{ id: 8888, name: 'Dev User', email: '<EMAIL>', phone: '+60123456795', role_id: 5 }
```

### 🔐 用户权限配置
**文件**: `js/config/user-permissions-config.js` **[行 169-620]**

**权限配置结构** (`USER_PERMISSION_CONFIG`):
```js
restrictedUsers: {
  // 受限合作伙伴
  '<EMAIL>': {
    priceDisplay: { canViewOtaPrice: false, canViewDriverFee: true },
    features: { canUsePaging: false },
    channels: { 
      restricted: true, 
      allowedChannels: ['JR Coach Credit', 'JR Coach Cash', 'JR COACH SERVICES - C1'],
      defaultChannel: 'JR Coach Credit'
    }
  },
  
  '<EMAIL>': {
    priceDisplay: { canViewOtaPrice: false, canViewDriverFee: false },
    features: { canUsePaging: true },
    channels: { 
      restricted: true, 
      allowedChannels: ['UCSI - Cheras', 'UCSI - Port Dickson', 'Student Travel'],
      defaultChannel: 'UCSI - Cheras'
    }
  },

  // 测试账户
  '<EMAIL>': {
    priceDisplay: { canViewOtaPrice: true, canViewDriverFee: true },
    features: { canUsePaging: true },
    channels: { 
      restricted: true, 
      allowedChannels: ['final_deployment_test', 'success_test'],
      defaultChannel: 'final_deployment_test'
    }
  }
}
```

### 🗂️ 下拉菜单静态数据
**文件**: `js/api-service.js` **[行 37-131]**

**子分类选项** (`subCategories`):
```js
[
  { id: 2, name: 'Pickup' },
  { id: 3, name: 'Dropoff' },
  { id: 4, name: 'Charter' }
]
```

**车型选项** (`carTypes`):
```js
[
  { id: 38, name: '4 Seater Hatchback (3 passenger, 2 x L size luggage)', passengerLimit: 3 },
  { id: 5, name: '5 Seater (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
  { id: 33, name: 'Premium 5 Seater (Mercedes/BMW Only) (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
  { id: 37, name: 'Extended 5 (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
  { id: 35, name: '7 Seater SUV (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
  { id: 15, name: '7 Seater MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
  { id: 16, name: 'Standard Size MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
  { id: 31, name: 'Luxury Mpv (Serena) (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
  { id: 32, name: 'Velfire/ Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
  { id: 36, name: 'Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
  { id: 20, name: '10 Seater MPV / Van (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
  { id: 30, name: '12 seat Starex (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
  { id: 23, name: '14 Seater Van (10 passenger, 10 x L size luggage)', passengerLimit: 10 },
  { id: 24, name: '18 Seater Van (12 passenger, 12 x L size luggage)', passengerLimit: 12 },
  { id: 25, name: '30 Seat Mni Bus (29 passenger, 29 x L size luggage)', passengerLimit: 29 },
  { id: 26, name: '44 Seater Bus (43 passenger, 43 x L size luggage)', passengerLimit: 43 },
  { id: 34, name: 'Ticket (N/A passenger, N/A luggage)', passengerLimit: 0 },
  { id: 39, name: 'Ticket (Non-Malaysian) (N/A passenger, N/A luggage)', passengerLimit: 0 }
]
```

**行驶区域选项** (`drivingRegions`):
```js
[
  { id: 1, name: 'Kl/selangor (KL)' },
  { id: 2, name: 'Penang (PNG)' },
  { id: 3, name: 'Johor (JB)' },
  { id: 4, name: 'Sabah (SBH)' },
  { id: 5, name: 'Singapore (SG)' },
  { id: 6, name: '携程专车 (CTRIP)' },
  { id: 8, name: 'Complete (COMPLETE)' },
  { id: 9, name: 'Paging (PG)' },
  { id: 10, name: 'Charter (CHRT)' },
  { id: 12, name: 'Malacca (MLK)' },
  { id: 13, name: 'SMW (SMW)' }
]
```

**语言选项** (`languages`):
```js
[
  { id: 2, name: 'English (EN)' },
  { id: 3, name: 'Malay (MY)' },
  { id: 4, name: 'Chinese (CN)' }
]
```

### 🔗 表单字段映射
**文件**: `js/managers/form-manager.js` **[行 698-740]**

**字段映射配置** (`fieldMapping`):
```js
{
  'customer_name': 'customer_name',
  'customer_contact': 'customer_contact',
  'customer_email': 'customer_email',
  'pickup': 'pickup',
  'destination': 'destination',
  'date': 'date',
  'time': 'time',
  'passenger_number': 'passenger_number',
  'luggage_number': 'luggage_number',
  'flight_number': 'flight_info',
  'flight_info': 'flight_info',
  'ota_price': 'ota_price',
  'currency': 'currency',
  'ota_reference_number': 'ota_reference_number',
  'extra_requirement': 'extra_requirement',
  'remark': 'remark',
  'sub_category_id': 'sub_category_id',
  'car_type_id': 'car_type_id',
  'incharge_by_backend_user_id': 'incharge_by_backend_user_id',
  'driving_region_id': 'driving_region_id'
}
```

### 🔄 数据关联说明
- **渠道-用户关联**: `USER_PERMISSION_CONFIG.restrictedUsers[email].channels.allowedChannels` 引用 `COMPLETE_CHANNEL_LIST`
- **用户ID-邮箱关联**: `COMPLETE_USER_LIST[].id` 对应 `USER_PERMISSION_CONFIG.restrictedUsers[id]`
- **字段映射**: 表单提交时使用 `fieldMapping` 转换字段名
- **下拉选项**: 表单渲染时从 `staticData` 填充选项列表

---
## 1. 用户与权限静态数据

**文件**：`js/config/user-permissions-config.js` (720行)

主要静态结构：
* `COMPLETE_USER_LIST` (Array<Object>)：系统已知后台用户基线数据；字段：`{ id, name, email, phone, role_id }` **[行 69-82]**
* `COMPLETE_CHANNEL_LIST` (Array<string>)：全量可用 OTA 渠道名称（含测试 / 别名 / 占位符 `Other`）**[行 84-198]**。2025-08-19 已补充：`final_deployment_test`, `success_test`, `Klook test`, `Heycar test`, `KKDAY SGD`, `Refund`, `Sativa - ID - Cynthia HTP`, `Kai - TC`, `JR COACH SERVICES - HTC - C1`。
* `USER_PERMISSION_CONFIG`：**[行 169-620]**
  * `restrictedUsers`：按邮箱或ID键控的精细权限对象：
    * `priceDisplay`: { canViewOtaPrice, canViewDriverFee }
    * `features`: { canUsePaging }
    * `channels`: { restricted: boolean, allowedChannels: string[] | null, defaultChannel? }
  * `defaultPermissions`：默认放行策略（channels.restricted = false）。
  * `config`: 系统级参数（`enabled`, `caseSensitive`, `debugMode`, `cacheTimeout`, `version`, `lastUpdated`）。
* `PERMISSION_TEMPLATES`：权限模板（`FULLY_RESTRICTED`, `PRICE_RESTRICTED`, `CHANNEL_RESTRICTED`, `FULL_ACCESS`）。

用途：
* 表单渠道下拉受限渲染（`getAllowedChannels`）
* 价格字段显示/隐藏、语言 Paging 选项控制
* 为运行期权限管理器提供同步基线（无外部请求时的离线来源）

维护建议：
* 将 `COMPLETE_USER_LIST` 与后端 `/user` 接口对比的差异自动化（脚本化校验）。
* 别名渠道（如 `Kai - TC`, `JR COACH SERVICES - HTC - C1`）后续可引入映射层：`aliasMap = { 'Kai - TC': 'Kai - TC1', ... }`，防止主列表污染。
* 建议拆分为：`permissions.users.js`, `permissions.channels.js`, `permissions.templates.js` 便于按域发布。

---
## 2. OTA 渠道静态数据

当前权威来源：同上 `COMPLETE_CHANNEL_LIST` **[行 84-198]**。

已废弃文件：`js/ota-channel-config.js` (338行)
* 含：`otaChannelMapping`（用户特定渠道）**[行 7-290]**；`commonChannels`（通用列表，含 value/text）**[行 292-338]**。
- 被标记为 Deprecated，仅保留避免旧脚本访问时崩溃；对外暴露一个降级空结构（`commonChannels: []`）。

与现行机制差异：
* 新权限系统用一维字符串数组；旧结构使用 `{ value, text }` 对象数组。
* 旧文件冗余的分组（酒店/代理/GMH/个人等）可迁移成分类元数据（见统一抽离建议）。

风险：
* 仍有测试 HTML (`tests/test-ota-mapping.html`) 引用旧命名空间 `window.OTA.otaChannelMapping`。需要计划阶段性彻底移除或复用映射层。

---
## 3. 业务字典（子分类 / 车型 / 行驶区域 / 语言）

**文件**：`js/api-service.js` 内 `this.staticData` **[行 37-131]** (总文件1374行)

结构：
```js
staticData = {
  backendUsers: [...],            // 同 1 类似（但可能滞后）
  subCategories: [ { id, name } ],
  carTypes: [ { id, name, passengerLimit } ],
  drivingRegions: [ { id, name } ],
  languages: [ { id, name } ],
  lastUpdated: null
};
```
说明：
* 这是 ApiService 的“离线基线缓存”来源；在未命中 API/或初始化早期用于填充表单下拉 (`FormManager.populateFormOptions`)。
* 车类型 passengerLimit 在前端用于验证人数。若后端参数变更（新增车型ID），需更新此列表避免表单缺项。

维护建议：
* 提供 `scripts/generate-static-data.js` 调用后端 `/dict/*` 接口导出 JSON，覆盖写入。
* 增加数据版本号：`staticData.version = 'YYYYMMDDHHmm'`。
* 与 `user-permissions-config.js` 的用户列表存在重复——建议单一来源（权限文件抽离用户或 ApiService 动态 fetch）。

**补充**：**字段映射配置**
* **文件**：`js/managers/form-manager.js` **[行 698-740]** (总文件2239行)
* **内容**：`fieldMapping` 对象，前端表单字段到后端API字段的映射关系
* **用途**：表单数据提交时的字段名转换，确保前后端数据一致性

---
## 4. 酒店数据库系统

### 4.1 完整酒店数据库
**文件**：`hotels_by_region.js` (21,470行)

静态结构：
* `rawData.hotels_by_region`：4264个酒店记录，8个区域分布 **[行 17-21470]**
* `rawData.metadata`：**[行 3-16]**
  - `total_hotels`: 4264
  - `total_regions`: 8  
  - `processing_stats`: 翻译统计、覆盖率信息
* `rawData.region_statistics`：各区域酒店数量和英文覆盖率 **[行 3-16]**

数据规模：21,470行代码，包含完整的酒店中英文名称映射

### 4.2 精简酒店数据
**文件**：`js/hotel-data-essential.js` (409行)

静态结构：
* `ESSENTIAL_HOTEL_DATA`：150家核心酒店信息 **[行 19-409]**
* 字段：`{ chinese, english, region, priority }`
* 优化：50KB vs 500KB完整版，实现62%启动性能提升

### 4.3 酒店数据变体
相关文件：
- `js/hotel-data-complete.js`：从hotels_by_region.json提取的完整数据集
- `js/hotel-data-inline.js`：轻量级酒店数据，补充完整数据
- `js/hotel-name-database.js`：酒店名称映射和检索数据库

用途：
* 地址智能翻译和酒店名称识别
* 多订单处理中的酒店名称标准化
* 按需加载策略：启动时加载精简版，运行时按需加载完整数据库

维护建议：
* 建立酒店数据自动同步机制，定期从外部数据源更新
* 监控精简版数据覆盖率，确保核心酒店不遗漏
* 考虑地理位置分区加载策略，进一步优化性能

---
## 5. 系统配置与构建信息

### 5.1 构建信息
**文件**：`build-info.js` (1行)

静态结构：
```js
window.BUILD_INFO = {
  version: "2.0.3",
  buildHash: "...",
  generatedAt: "...",
  environment: "local",
  gitCommit: null,
  node: "v22.12.0",
  files: {
    manifestHash: "...",
    loaderHash: "...", 
    mainHash: "..."
  }
};
```
**完整数据结构**：**[行 1]** (单行，自动生成)

用途：
* 版本追踪和缓存失效策略
* 部署一致性验证
* 构建环境信息记录

### 5.2 生产环境配置
**文件**：`deployment/production-config.js` (615行)

静态配置对象（615行）：
* `environment`: { name, version, buildDate, debug } **[行 10-15]**
* `learningSystem`: { enabled, autoLearning, confidenceThreshold, maxRules } **[行 16-25]**
* `storage`: { retentionDays, compressionEnabled, backupEnabled, encryptionEnabled } **[行 26-35]**
* `cache`: { maxSize, maxMemoryUsage, defaultTTL, preloadEnabled } **[行 36-45]**
* `performance`: { monitoringEnabled, sampleRate, metricsInterval, alertThresholds } **[行 46-60]**
* `logging`: { level, enableConsole, enableStorage, maxLogSize } **[行 61-70]**
* `security`: { enableEncryption, enableIntegrityCheck, maxFailedAttempts } **[行 71-80]**

维护建议：
* 拆分环境特定配置，避免单文件过大
* 敏感配置项移至环境变量
* 建立配置变更审计机制

---
## 6. 脚本加载与多语言配置

### 6.1 脚本加载清单
**文件**：`js/core/script-manifest.js` (185行)

静态结构（185行）：
* `phases`：5阶段加载架构 **[行 34-93]**
  - infrastructure：基础设施层
  - configuration：配置和类定义
  - services：服务实现层
  - managers：管理器实例化
  - launch：验证和启动
* `version`：2.0.3版本信息 **[行 164]**
* `optimizations`：性能优化记录 **[行 165-173]**

用途：
* 脚本依赖关系管理
* 启动性能优化（15-20%提升）
* 避免重复注册警告

### 6.2 多语言配置
**文件**：`js/i18n.js` (1011行)

静态结构（1011行）：
* `translations.zh`：中文翻译资源（500+条目）**[行 65-550]**
* `translations.en`：英文翻译资源（500+条目）**[行 551-1011]**
* 覆盖范围：
  - `common.*`：通用界面文本
  - `header.*`：头部导航
  - `form.*`：表单字段和验证
  - `button.*`：按钮文本
  - `message.*`：提示信息
  - `validation.*`：验证错误信息

维护建议：
* 建立翻译缺失检测机制
* 考虑外部化翻译资源（JSON文件）
* 支持动态语言包加载

---
## 7. 环境与特性配置

**文件**：`production-config.js`（类 `ProductionConfig`，615行）

静态配置对象层次：
* `app`: { name, version, build, environment } **[行 10-15]**
* `api`: { baseURL, timeout, retries, retryDelay } **[行 16-25]**
* `gemini`: { endpoint, timeout, maxTokens, temperature } **[行 26-35]**
* `monitoring`: { enabled, sampleRate, reportInterval, endpoints { metrics, errors, vitals } } **[行 46-60]**
* `errorTracking`: { enabled, maxErrors, reportImmediately, alertThresholds { errorRate, criticalErrors, timeWindow } } **[行 61-75]**
* `cache`: { enabled, version, strategies{}, ttl{} } **[行 36-45]**
* `security`: { enableCSP, enableHSTS, apiKeyRotationInterval, sessionTimeout, maxLoginAttempts } **[行 71-85]**
* `features`: { imageUpload, multiOrderProcessing, offlineMode, darkMode, analytics, debugMode } **[行 86-95]**
* `cdn`: { enabled, baseURL, assets[], version } **[行 96-105]**
* `pwa`: { enabled, serviceWorker, manifest, updatePrompt, installPrompt } **[行 106-115]**
* 环境覆盖：`overrides.development|staging|production` **[行 116-200]**。

用途：
* 初始化全局运行策略（CSP、监控采样、功能开关）。

维护建议：
- 拆分：`config/base-env.js` + `config/env/production.js`（减少类构造影响页面首屏）。
- 将硬编码 endpoint（如 Gemini）转 `.env` 注入（Netlify 环境变量 + 构建注入）。

---
## 8. 废弃/兼容静态数据

| 文件 | 状态 | 内容 | 保留原因 |
|------|------|------|----------|
| `js/ota-channel-config.js` | Deprecated | 用户专属渠道映射 + 通用渠道对象数组 | 旧测试脚本仍引用，尚未全面迁移 |
| `js/core/core.js.backup` | 备份 | 旧核心脚本 | 回滚/历史对比 |
| `archive/` 子目录 | 归档 | 旧字段映射、标准化、适配器 | 历史参考，不应再主动引用 |
| `archive/field-mapping-config.js` | 归档 | 旧字段映射配置（220行） | 架构简化前的多层映射系统 |
| `archive/global-field-standardization-layer.js` | 已移除 | 全局字段标准化层 | 架构简化，已整合到组件内部 |

新发现废弃说明：
* **渠道数据整合**：`js/ota-channel-config.js` 中的渠道列表已全部迁移到 `user-permissions-config.js`，实现单一数据源
* **字段映射简化**：移除了5层字段映射架构，简化为1层直接映射，提升维护性
* **脚本清单注释**：`js/core/script-manifest.js` 中明确标记已废弃的文件和移除原因

建议：设立 ESLint 规则扫描直接访问 `window.OTA.otaChannelMapping` 的地方，提示迁移。

---
## 9. 其他潜在静态数据文件（占位扫描建议）

已确认的其他静态数据文件：
- ✅ `js/i18n.js`：多语言翻译资源（已记录在第6节）
- ✅ `js/core/script-manifest.js`：脚本加载清单（已记录在第6节）
- ✅ `build-info.js`：构建信息和版本数据（已记录在第5节）
- ✅ `deployment/production-config.js`：生产环境配置（已记录在第5节）

潜在扫描建议：
- `js/managers/*` 某些 manager 中可能内置默认阈值 / 退避参数（如动画、重试策略）
- `deployment/*.js` 中的监控/校验阈值（不影响运行时业务表单，但属于静态配置）
- `sw.js`：Service Worker 中的缓存策略和离线数据
- `netlify/functions/*.js`：Serverless 函数中的配置常量

后续可执行脚本模式：
- 搜索正则：`/:\s*\[|=\s*\[|=\s*{.*version.*}/` 结合文件白名单输出候选。

---
## 10. 统一抽离与治理建议

| 目标 | 方案 | 收益 |
|------|------|------|
| 渠道分类元数据 | 新建 `data/channels.json`：`{ name, category, alias?:[], active:true }` | 支持 UI 分组、别名映射、快速禁用 |
| 用户 & 权限分离 | 拆成 `permissions/users.json` 与 `permissions/rules.json` | 降低冲突，清晰职责 |
| 字典自动同步 | `npm run sync:dict` 调用后端写入 `data/dicts/*.json` | 减少手工遗漏 |
| 别名标准化 | `channel-alias-map.json` + 表单提交前归一化 | 避免主列表膨胀 |
| 版本校验 | 构建脚本生成 `static-data-hash.json` | 发布可追踪差异 |
| 监控静态漂移 | 启动时对比后端最新摘要（hash） | 及时预警数据过期 |
| 酒店数据优化 | 分层加载：精简版启动 + 完整版按需 | 62%启动性能提升 |
| 多语言资源化 | 外部化翻译文件，支持动态加载 | 减少主包体积，支持更多语言 |

---
## 11. 变更记录

- 2025-08-19 初始化文档；补充渠道别名与测试条目；提出抽离方案
- 2025-08-19 新增酒店数据库系统（4个变体文件，21470行数据）
- 2025-08-19 新增系统配置与构建信息（构建信息、生产配置）
- 2025-08-19 新增脚本加载与多语言配置（脚本清单、i18n翻译）
- 2025-08-19 完善废弃文件追踪，记录架构简化过程
- 2025-08-19 添加所有静态数据的精确行数信息，便于快速定位和维护
- **2025-08-19 新增核心业务数据速览区，集中展示渠道列表、用户ID、权限配置、字段映射ID，便于日常查看和维护**

---
## 12. 数据统计总览

| 类别 | 文件数 | 代码行数 | 主要数据量 | 关键数据行范围 |
|------|--------|----------|------------|----------------|
| 用户权限 | 1 | 720行 | 114个渠道，用户权限配置 | `js/config/user-permissions-config.js:69-620` |
| 业务字典 | 1 | 1374行 | 用户、车型、区域、语言数据 | `js/api-service.js:37-131` |
| 酒店数据 | 4 | 22000+行 | 4264个酒店，8个区域 | `hotels_by_region.js:17-21470` |
| 系统配置 | 3 | 800+行 | 构建信息、生产配置、脚本清单 | `build-info.js:1`, `deployment/production-config.js:10-615` |
| 多语言 | 1 | 1011行 | 500+中英文翻译条目 | `js/i18n.js:65-1011` |
| 废弃文件 | 5+ | 未计 | 归档的旧架构文件 | `archive/field-mapping-config.js:1-220` |
| **总计** | **15+** | **约26000行** | **完整业务静态数据** | **详见各节行数标注** |

**性能优化成果**：
- 酒店数据分层加载：62%启动性能提升
- 脚本5阶段架构：15-20%启动性能提升  
- 字段映射架构简化：从5层减少到1层
- 渠道数据单一来源：消除重复维护

---
## 13. 快速校验脚本示例（可放 /scripts）

```js
// check-static-integrity.js
import fs from 'fs';
import crypto from 'crypto';

const files = [
  { file: 'js/config/user-permissions-config.js', keyLines: '69-620' },
  { file: 'js/api-service.js', keyLines: '37-131' },
  { file: 'production-config.js', keyLines: '10-615' },
  { file: 'hotels_by_region.js', keyLines: '17-21470' },
  { file: 'js/i18n.js', keyLines: '65-1011' },
  { file: 'js/core/script-manifest.js', keyLines: '34-173' }
];

const report = files.map(({ file, keyLines }) => {
  const content = fs.readFileSync(file,'utf8');
  const hash = crypto.createHash('sha256').update(content).digest('hex').slice(0,12);
  const channelCount = /COMPLETE_CHANNEL_LIST[\s\S]*?\[([\s\S]*?)\];/.test(content)
    ? (content.match(/COMPLETE_CHANNEL_LIST[\s\S]*?\[([\s\S]*?)\];/)[1].match(/'[^']+'/g)||[]).length
    : null;
  const hotelCount = /total_hotels['":\s]*(\d+)/.test(content)
    ? content.match(/total_hotels['":\s]*(\d+)/)[1]
    : null;
  const translationCount = /translations\.zh[\s\S]*?}/.test(content)
    ? (content.match(/translations\.zh[\s\S]*?}/)[0].match(/'[^']+'/g)||[]).length
    : null;
  
  return { 
    file, 
    keyLines,
    hash, 
    channelCount, 
    hotelCount,
    translationCount,
    size: `${Math.round(content.length/1024)}KB`
  };
});
console.table(report);
```

---
## 14. 后续待办（可分批实施）

1. 扫描 & 抽离渠道别名映射。
2. 建立后端字典同步脚本。
3. 引入 JSON Schema 校验静态数据格式。
4. 构建时生成数据哈希并写入 `deployment-validation-report.json`。
5. 清理废弃 `ota-channel-config.js` 及相关测试引用（或替换为 shim）。
6. 实施酒店数据按需加载策略优化。
7. 外部化多语言翻译资源。
8. 建立静态数据版本管理机制。

---
如需：我可以继续生成校验脚本或把上述拆分文件雏形落盘，告知即可。
