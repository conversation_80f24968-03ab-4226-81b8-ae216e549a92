# 监听器集成技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[用户浏览器] --> B[React前端应用]
    B --> C[监听器注册中心]
    C --> D[全局事件协调器]
    C --> E[表单验证监听器]
    C --> F[自动保存监听器]
    C --> G[拖拽上传监听器]
    H[Supabase SDK] --> I[Supabase服务]
    B --> H
    
    subgraph "前端层"
        B
        C
        D
        E
        F
        G
    end
    
    subgraph "服务层（由Supabase提供）"
        I
    end
    
    subgraph "监听器管理层"
        J[错误处理器]
        K[性能监控器]
        L[测试框架]
    end
    
    C --> J
    C --> K
    C --> L
```

## 2. 技术描述

- **前端**: React@18 + tailwindcss@3 + vite
- **后端**: Supabase（认证、数据库、存储）
- **监听器框架**: 自定义事件管理系统
- **状态管理**: 现有的StateManager + 新增监听器状态
- **文件上传**: 现有ImageUploadManager + 拖拽扩展

## 3. 路由定义

| 路由 | 用途 | 新增监听器功能 |
|------|------|----------------|
| / | 主页，显示订单表单和导航 | 表单验证、自动保存、拖拽上传 |
| /multi-order | 多订单处理页面 | 批量操作监听、状态同步 |
| /history | 订单历史页面 | 数据加载监听、筛选监听 |
| /settings | 设置页面 | 配置变更监听、主题切换 |

## 4. API定义

### 4.1 监听器管理API

#### 监听器注册
```typescript
interface ListenerRegistration {
  name: string;
  instance: EventListener;
  priority: number;
  dependencies?: string[];
}

POST /api/listeners/register
```

请求:
| 参数名 | 参数类型 | 是否必需 | 描述 |
|--------|----------|----------|------|
| name | string | true | 监听器名称 |
| type | string | true | 监听器类型 |
| config | object | false | 配置参数 |

响应:
| 参数名 | 参数类型 | 描述 |
|--------|----------|------|
| success | boolean | 注册状态 |
| listenerId | string | 监听器ID |

示例:
```json
{
  "name": "formValidation",
  "type": "validation",
  "config": {
    "realTime": true,
    "debounceMs": 300
  }
}
```

#### 监听器状态查询
```typescript
GET /api/listeners/status
```

响应:
| 参数名 | 参数类型 | 描述 |
|--------|----------|------|
| listeners | array | 监听器列表 |
| totalActive | number | 活跃监听器数量 |
| errors | array | 错误列表 |

### 4.2 自动保存API

#### 保存表单数据
```typescript
POST /api/autosave/form
```

请求:
| 参数名 | 参数类型 | 是否必需 | 描述 |
|--------|----------|----------|------|
| formId | string | true | 表单标识 |
| data | object | true | 表单数据 |
| timestamp | number | true | 时间戳 |

响应:
| 参数名 | 参数类型 | 描述 |
|--------|----------|------|
| saved | boolean | 保存状态 |
| version | string | 数据版本 |

#### 恢复表单数据
```typescript
GET /api/autosave/form/{formId}
```

响应:
| 参数名 | 参数类型 | 描述 |
|--------|----------|------|
| data | object | 表单数据 |
| timestamp | number | 保存时间 |
| version | string | 数据版本 |

## 5. 服务器架构图

```mermaid
graph TD
    A[客户端/前端] --> B[控制器层]
    B --> C[服务层]
    C --> D[仓储层]
    D --> E[(Supabase数据库)]
    
    subgraph "监听器服务层"
        F[监听器管理服务]
        G[事件分发服务]
        H[状态同步服务]
        I[错误处理服务]
    end
    
    C --> F
    C --> G
    C --> H
    C --> I
    
    subgraph "服务器"
        B
        C
        D
        F
        G
        H
        I
    end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    LISTENER_REGISTRY ||--o{ LISTENER_INSTANCE : manages
    LISTENER_INSTANCE ||--o{ EVENT_LOG : generates
    LISTENER_INSTANCE ||--o{ ERROR_LOG : produces
    AUTO_SAVE_DATA ||--|| FORM_SESSION : belongs_to
    FORM_SESSION ||--o{ VALIDATION_RESULT : has
    
    LISTENER_REGISTRY {
        string id PK
        string name
        string type
        json config
        int priority
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    LISTENER_INSTANCE {
        string id PK
        string registry_id FK
        string session_id
        string status
        json state
        timestamp last_active
    }
    
    EVENT_LOG {
        string id PK
        string listener_id FK
        string event_type
        json event_data
        timestamp occurred_at
        int processing_time_ms
    }
    
    ERROR_LOG {
        string id PK
        string listener_id FK
        string error_type
        text error_message
        json stack_trace
        timestamp occurred_at
        boolean is_resolved
    }
    
    AUTO_SAVE_DATA {
        string id PK
        string session_id FK
        string form_id
        json form_data
        string version
        timestamp saved_at
        boolean is_current
    }
    
    FORM_SESSION {
        string id PK
        string user_id
        string form_type
        timestamp started_at
        timestamp last_activity
        boolean is_active
    }
    
    VALIDATION_RESULT {
        string id PK
        string session_id FK
        string field_name
        boolean is_valid
        text error_message
        timestamp validated_at
    }
```

### 6.2 数据定义语言

#### 监听器注册表 (listener_registry)
```sql
-- 创建监听器注册表
CREATE TABLE listener_registry (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL,
    config JSONB DEFAULT '{}',
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_listener_registry_type ON listener_registry(type);
CREATE INDEX idx_listener_registry_priority ON listener_registry(priority DESC);
CREATE INDEX idx_listener_registry_active ON listener_registry(is_active);

-- 初始化数据
INSERT INTO listener_registry (name, type, priority, config) VALUES
('globalEventCoordinator', 'global', 100, '{"events": ["click", "keydown", "resize"]}'),
('formValidation', 'validation', 90, '{"realTime": true, "debounceMs": 300}'),
('autoSave', 'persistence', 80, '{"intervalMs": 30000, "maxVersions": 10}'),
('dragDropUpload', 'upload', 70, '{"maxFileSize": 10485760, "acceptedTypes": ["image/*", ".pdf"]}')
ON CONFLICT (name) DO NOTHING;
```

#### 监听器实例表 (listener_instances)
```sql
-- 创建监听器实例表
CREATE TABLE listener_instances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    registry_id UUID REFERENCES listener_registry(id) ON DELETE CASCADE,
    session_id VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paused', 'error', 'destroyed')),
    state JSONB DEFAULT '{}',
    last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_listener_instances_session ON listener_instances(session_id);
CREATE INDEX idx_listener_instances_status ON listener_instances(status);
CREATE INDEX idx_listener_instances_last_active ON listener_instances(last_active DESC);
```

#### 事件日志表 (event_logs)
```sql
-- 创建事件日志表
CREATE TABLE event_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    listener_id UUID REFERENCES listener_instances(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB DEFAULT '{}',
    occurred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processing_time_ms INTEGER DEFAULT 0
);

-- 创建索引
CREATE INDEX idx_event_logs_listener ON event_logs(listener_id);
CREATE INDEX idx_event_logs_type ON event_logs(event_type);
CREATE INDEX idx_event_logs_occurred ON event_logs(occurred_at DESC);

-- 创建分区表（按月分区）
CREATE TABLE event_logs_y2024m01 PARTITION OF event_logs
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

#### 错误日志表 (error_logs)
```sql
-- 创建错误日志表
CREATE TABLE error_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    listener_id UUID REFERENCES listener_instances(id) ON DELETE CASCADE,
    error_type VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace JSONB,
    occurred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_resolved BOOLEAN DEFAULT false
);

-- 创建索引
CREATE INDEX idx_error_logs_listener ON error_logs(listener_id);
CREATE INDEX idx_error_logs_type ON error_logs(error_type);
CREATE INDEX idx_error_logs_resolved ON error_logs(is_resolved);
CREATE INDEX idx_error_logs_occurred ON error_logs(occurred_at DESC);
```

#### 自动保存数据表 (auto_save_data)
```sql
-- 创建自动保存数据表
CREATE TABLE auto_save_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(100) NOT NULL,
    form_id VARCHAR(100) NOT NULL,
    form_data JSONB NOT NULL,
    version VARCHAR(50) NOT NULL,
    saved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_current BOOLEAN DEFAULT true
);

-- 创建索引
CREATE INDEX idx_auto_save_session ON auto_save_data(session_id);
CREATE INDEX idx_auto_save_form ON auto_save_data(form_id);
CREATE INDEX idx_auto_save_current ON auto_save_data(is_current);
CREATE INDEX idx_auto_save_saved_at ON auto_save_data(saved_at DESC);

-- 创建唯一约束
CREATE UNIQUE INDEX idx_auto_save_current_unique 
ON auto_save_data(session_id, form_id) 
WHERE is_current = true;
```

#### 表单会话表 (form_sessions)
```sql
-- 创建表单会话表
CREATE TABLE form_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(100),
    form_type VARCHAR(50) NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- 创建索引
CREATE INDEX idx_form_sessions_user ON form_sessions(user_id);
CREATE INDEX idx_form_sessions_type ON form_sessions(form_type);
CREATE INDEX idx_form_sessions_active ON form_sessions(is_active);
CREATE INDEX idx_form_sessions_activity ON form_sessions(last_activity DESC);
```

#### 验证结果表 (validation_results)
```sql
-- 创建验证结果表
CREATE TABLE validation_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES form_sessions(id) ON DELETE CASCADE,
    field_name VARCHAR(100) NOT NULL,
    is_valid BOOLEAN NOT NULL,
    error_message TEXT,
    validated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_validation_results_session ON validation_results(session_id);
CREATE INDEX idx_validation_results_field ON validation_results(field_name);
CREATE INDEX idx_validation_results_valid ON validation_results(is_valid);
CREATE INDEX idx_validation_results_validated ON validation_results(validated_at DESC);
```

#### 权限设置
```sql
-- 为匿名用户授予基本读取权限
GRANT SELECT ON listener_registry TO anon;
GRANT SELECT ON event_logs TO anon;

-- 为认证用户授予完全权限
GRANT ALL PRIVILEGES ON listener_registry TO authenticated;
GRANT ALL PRIVILEGES ON listener_instances TO authenticated;
GRANT ALL PRIVILEGES ON event_logs TO authenticated;
GRANT ALL PRIVILEGES ON error_logs TO authenticated;
GRANT ALL PRIVILEGES ON auto_save_data TO authenticated;
GRANT ALL PRIVILEGES ON form_sessions TO authenticated;
GRANT ALL PRIVILEGES ON validation_results TO authenticated;

-- 启用行级安全策略
ALTER TABLE auto_save_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE validation_results ENABLE ROW LEVEL SECURITY;

-- 创建安全策略
CREATE POLICY "用户只能访问自己的自动保存数据" ON auto_save_data
FOR ALL USING (session_id IN (
    SELECT id::text FROM form_sessions WHERE user_id = auth.uid()::text
));

CREATE POLICY "用户只能访问自己的表单会话" ON form_sessions
FOR ALL USING (user_id = auth.uid()::text);
```

#### 触发器和函数
```sql
-- 创建更新时间戳函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为监听器注册表添加更新时间戳触发器
CREATE TRIGGER update_listener_registry_updated_at 
BEFORE UPDATE ON listener_registry 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建自动清理过期数据函数
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- 清理30天前的事件日志
    DELETE FROM event_logs WHERE occurred_at < NOW() - INTERVAL '30 days';
    
    -- 清理7天前的错误日志（已解决的）
    DELETE FROM error_logs 
    WHERE occurred_at < NOW() - INTERVAL '7 days' AND is_resolved = true;
    
    -- 清理非当前版本的自动保存数据（保留最近10个版本）
    WITH ranked_saves AS (
        SELECT id, 
               ROW_NUMBER() OVER (PARTITION BY session_id, form_id ORDER BY saved_at DESC) as rn
        FROM auto_save_data 
        WHERE is_current = false
    )
    DELETE FROM auto_save_data 
    WHERE id IN (SELECT id FROM ranked_saves WHERE rn > 10);
    
    -- 清理非活跃的表单会话（7天未活动）
    UPDATE form_sessions 
    SET is_active = false 
    WHERE last_activity < NOW() - INTERVAL '7 days' AND is_active = true;
END;
$$ LANGUAGE plpgsql;

-- 创建定时清理任务（需要pg_cron扩展）
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * *', 'SELECT cleanup_old_data();');
```

## 7. 集成实施计划

### 7.1 第一阶段：基础监听器框架

**目标**：建立监听器注册和管理机制

**任务清单**：
1. ✅ 创建监听器注册中心类
2. ✅ 实现基础事件分发机制
3. ✅ 建立错误处理框架
4. ✅ 创建数据库表结构
5. ✅ 实现监听器生命周期管理

**验收标准**：
- 监听器可以正常注册和注销
- 事件可以正确分发到对应监听器
- 错误可以被捕获和处理
- 数据库操作正常

### 7.2 第二阶段：核心监听器实现

**目标**：实现表单验证和自动保存功能

**任务清单**：
1. ✅ 实现FormValidationListener
2. ✅ 实现AutoSaveListener
3. ✅ 集成到现有表单系统
4. ✅ 添加用户界面反馈
5. ✅ 实现数据持久化

**验收标准**：
- 表单字段可以实时验证
- 表单数据可以自动保存和恢复
- 用户界面有适当的反馈
- 数据可以正确存储和检索

### 7.3 第三阶段：高级功能扩展

**目标**：实现拖拽上传和状态同步

**任务清单**：
1. ✅ 实现DragDropUploadListener
2. ✅ 实现StateSyncListener
3. ✅ 添加性能监控
4. ✅ 实现测试框架
5. ✅ 优化用户体验

**验收标准**：
- 文件可以通过拖拽上传
- 组件状态可以正确同步
- 性能指标可以监控
- 测试覆盖率达到80%以上

## 8. 部署和运维

### 8.1 部署配置

```javascript
// 生产环境配置
const PRODUCTION_CONFIG = {
  listeners: {
    formValidation: {
      enabled: true,
      debounceMs: 300,
      showSuccessIndicator: false
    },
    autoSave: {
      enabled: true,
      intervalMs: 30000,
      maxVersions: 5
    },
    dragDropUpload: {
      enabled: true,
      maxFileSize: 10 * 1024 * 1024,
      acceptedTypes: ['image/*', '.pdf', '.doc', '.docx']
    }
  },
  monitoring: {
    errorReporting: true,
    performanceTracking: true,
    userAnalytics: false
  }
};

// 开发环境配置
const DEVELOPMENT_CONFIG = {
  listeners: {
    formValidation: {
      enabled: true,
      debounceMs: 100,
      showSuccessIndicator: true
    },
    autoSave: {
      enabled: true,
      intervalMs: 10000,
      maxVersions: 10
    },
    dragDropUpload: {
      enabled: true,
      maxFileSize: 50 * 1024 * 1024,
      acceptedTypes: ['*/*']
    }
  },
  monitoring: {
    errorReporting: true,
    performanceTracking: true,
    userAnalytics: true,
    debugMode: true
  }
};
```

### 8.2 监控和告警

```javascript
// 监控指标定义
const MONITORING_METRICS = {
  // 性能指标
  performance: {
    listenerResponseTime: { threshold: 100, unit: 'ms' },
    eventProcessingRate: { threshold: 1000, unit: 'events/min' },
    memoryUsage: { threshold: 50, unit: 'MB' }
  },
  
  // 错误指标
  errors: {
    errorRate: { threshold: 0.01, unit: 'percentage' },
    criticalErrors: { threshold: 0, unit: 'count' },
    listenerFailures: { threshold: 3, unit: 'count/hour' }
  },
  
  // 业务指标
  business: {
    formValidationSuccess: { threshold: 0.95, unit: 'percentage' },
    autoSaveSuccess: { threshold: 0.99, unit: 'percentage' },
    uploadSuccess: { threshold: 0.98, unit: 'percentage' }
  }
};
```

### 8.3 回滚策略

```javascript
// 功能开关配置
const FEATURE_FLAGS = {
  FORM_VALIDATION_LISTENER: {
    enabled: true,
    rollbackVersion: 'v1.0.0',
    canaryPercentage: 10
  },
  AUTO_SAVE_LISTENER: {
    enabled: true,
    rollbackVersion: 'v1.0.0',
    canaryPercentage: 20
  },
  DRAG_DROP_UPLOAD: {
    enabled: false, // 可以快速禁用
    rollbackVersion: 'v1.0.0',
    canaryPercentage: 5
  }
};

// 自动回滚条件
const ROLLBACK_CONDITIONS = {
  errorRateThreshold: 0.05, // 错误率超过5%
  performanceDegradation: 0.3, // 性能下降30%
  userComplaintThreshold: 10, // 用户投诉超过10个
  criticalBugDetected: true // 检测到严重bug
};
```

## 9. 安全考虑

### 9.1 数据安全

- **加密存储**：敏感数据使用AES-256加密
- **访问控制**：基于用户角色的权限管理
- **数据脱敏**：日志中的敏感信息自动脱敏
- **审计跟踪**：所有操作都有完整的审计日志

### 9.2 输入验证

```javascript
// 输入验证规则
const VALIDATION_RULES = {
  // XSS防护
  sanitizeInput: true,
  allowedTags: [],
  
  // SQL注入防护
  parameterizedQueries: true,
  inputEscaping: true,
  
  // 文件上传安全
  fileTypeValidation: true,
  virusScan: true,
  fileSizeLimit: true
};
```

### 9.3 网络安全

- **HTTPS强制**：所有通信使用HTTPS
- **CSRF保护**：实现CSRF令牌验证
- **速率限制**：API调用频率限制
- **内容安全策略**：CSP头部配置

## 10. 性能优化

### 10.1 前端优化

```javascript
// 性能优化配置
const PERFORMANCE_CONFIG = {
  // 事件防抖
  debounce: {
    input: 300,
    scroll: 100,
    resize: 250
  },
  
  // 虚拟化
  virtualization: {
    listThreshold: 100,
    tableThreshold: 50
  },
  
  // 缓存策略
  cache: {
    validationResults: 300000, // 5分钟
    formData: 1800000, // 30分钟
    userPreferences: 3600000 // 1小时
  }
};
```

### 10.2 数据库优化

```sql
-- 查询优化
CREATE INDEX CONCURRENTLY idx_event_logs_composite 
ON event_logs(listener_id, occurred_at DESC, event_type);

-- 分区策略
CREATE TABLE event_logs_current PARTITION OF event_logs
FOR VALUES FROM (CURRENT_DATE) TO (CURRENT_DATE + INTERVAL '1 month');

-- 统计信息更新
ANALYZE event_logs;
ANALYZE error_logs;
ANALYZE auto_save_data;
```

---

*本技术架构文档提供了监听器系统的完整实现方案，包括数据库设计、API定义、部署策略和安全考虑。建议按照分阶段实施计划逐步部署，确保系统稳定性和性能。*