/**
 * ============================================================================
 * 🚀 统一字段映射服务 - 简化架构的核心组件
 * ============================================================================
 *
 * @fileoverview 统一字段映射服务 - 解决数据契约不一致问题
 * @description 提供前端camelCase与API snake_case之间的统一映射
 *
 * @architecture 简化架构的核心组件
 * - 职责：统一所有字段映射逻辑，消除重复转换
 * - 原则：单一数据契约，明确的转换规则
 * - 策略：前端使用camelCase，API使用snake_case，中间统一转换
 *
 * @dependencies 依赖关系
 * 上游依赖：无（底层服务）
 * 下游依赖：
 * - form-manager.js (表单数据收集)
 * - api-service.js (API数据预处理)
 * - multi-order-transformer.js (订单数据转换)
 *
 * @coreFeatures 核心功能
 * - 🟢 前端到API字段映射
 * - 🟢 API到前端字段映射
 * - 🟢 数据类型转换和验证
 * - 🟢 默认值设置
 * - 🟢 必填字段检查
 */

(function() {
    'use strict';

    /**
     * 统一字段映射服务类
     * 解决数据契约不一致问题的核心组件
     */
    class UnifiedFieldMapper {
        constructor() {
            this.logger = this.getLogger();
            
            // 🚀 核心映射表：前端camelCase ↔ API snake_case
            // 🚀 注意：字段映射逻辑已移除，现在统一使用 snake_case 格式

            // � 新增: 负责人字段映射（前端隐藏字段）


            // �🚀 时间字段优先级映射（解决null值覆盖问题）


            // 🚀 必填字段定义
            this.REQUIRED_API_FIELDS = [
                'pickup', 'destination', 'date', 'time',
                'customer_name', 'ota_reference_number', 'ota_price',
                'sub_category_id', 'car_type_id', 'driving_region_id'
            ];

            // 🚀 默认值配置
            this.DEFAULT_VALUES = {
                'passenger_number': 1,
                'luggage_number': 0,
                'sub_category_id': 2,  // 默认接机
                'driving_region_id': 1, // 默认区域
                'languages_id_array': {"0": "2"}, // 默认英文（对象格式，符合API要求）
                // ✅ 确保价格字段始终存在（后端统计/分析需要，即使为0）
                'ota_price': 0,
                // ✅ 特殊服务字段默认值
                'baby_chair': false,
                'tour_guide': false,
                'meet_and_greet': false,
                'needs_paging_service': false
            };
        }

        /**
         * 数据预处理（保留数据类型转换和默认值设置）
         * @param {object} data - 输入数据（已经是snake_case格式）
         * @returns {object} 处理后的数据
         */
        processData(data) {
            console.log('🚀 unified-field-mapper.processData 开始执行', data);
            if (!data || typeof data !== 'object') {
                console.log('🚀 unified-field-mapper.processData 数据验证失败', data);
                return data;
            }

            const processedData = { ...data };
            console.log('🚀 unified-field-mapper.processData 复制数据完成', processedData);

            // 应用数据类型转换
            this.convertDataTypes(processedData);

            // 设置默认值
            this.applyDefaultValues(processedData);

            // 确保所有API期望的字段都存在（设为null如果缺失）
            const requiredAPIFields = [
                'customer_name', 'customer_contact', 'customer_email', 'ota', 'ota_reference_number',
                'flight_info', 'departure_time', 'arrival_time', 'flight_type', 'date', 'time',
                'pickup', 'destination', 'passenger_number', 'luggage_number', 'sub_category_id',
                'car_type_id', 'driving_region_id', 'baby_chair', 'tour_guide', 'meet_and_greet',
                'needs_paging_service', 'ota_price', 'currency', 'extra_requirement'
            ];

            requiredAPIFields.forEach(field => {
                if (!(field in processedData)) {
                    processedData[field] = null;
                }
            });

            // ✅ 负责人ID字段已统一为snake_case，无需转换

            // 🔧 负责人ID兜底：若缺失则根据当前登录邮箱匹配；仍失败则用1
            if (!processedData.incharge_by_backend_user_id) {
                try {
                    // 🔍 记录触发负责人ID映射的原因
                    console.log('🔍 unified-field-mapper: 开始负责人ID映射兜底逻辑', {
                        originalData: processedData.incharge_by_backend_user_id,
                        timestamp: new Date().toISOString()
                    });

                    // 优先使用 ApiService 提供的方法（包含系统数据匹配逻辑）
                    if (typeof window.getApiService === 'function') {
                        const apiService = window.getApiService?.();
                        logger.log('🔍 unified-field-mapper: 检查 ApiService 可用性', 'debug', {
                            hasGetApiService: true,
                            apiServiceExists: !!apiService,
                            hasGetDefaultBackendUserId: !!(apiService && typeof apiService.getDefaultBackendUserId === 'function')
                        });
                        
                        if (apiService && typeof apiService.getDefaultBackendUserId === 'function') {
                            const originalValue = processedData.incharge_by_backend_user_id;
                            processedData.incharge_by_backend_user_id = apiService.getDefaultBackendUserId();
                            logger.log('🔍 unified-field-mapper: 调用 ApiService.getDefaultBackendUserId()', 'debug', {
                                before: originalValue,
                                after: processedData.incharge_by_backend_user_id,
                                changed: originalValue !== processedData.incharge_by_backend_user_id
                            });
                        } else {
                            logger.log('❌ unified-field-mapper: ApiService.getDefaultBackendUserId 不可用', 'warning');
                        }
                    } else {
                        logger.log('❌ unified-field-mapper: window.getApiService 不存在', 'warning');
                    }
                    
                    // 邮箱→ID 兜底：仅使用 COMPLETE_USER_LIST（移除静态硬编码 directMap）
                    if (!processedData.incharge_by_backend_user_id) {
                        const email = window.getAppState?.()?.get('auth.user.email');
                        logger.log('🔍 unified-field-mapper: 尝试 COMPLETE_USER_LIST 兜底映射', 'debug', {
                            currentUserEmail: email,
                            hasCompleteUserList: !!window.OTA?.config?.COMPLETE_USER_LIST
                        });
                        
                        if (email) {
                            const list = window.OTA?.config?.COMPLETE_USER_LIST || [];
                            logger.log('🔍 unified-field-mapper: COMPLETE_USER_LIST 详细信息', 'debug', {
                                email: email,
                                completeUserListLength: list.length,
                                completeUserListSample: list.slice(0, 5).map(u => ({ email: u.email, id: u.id }))
                            });
                            
                            const found = list.find(u => (u.email || '').toLowerCase() === email.toLowerCase());
                            if (found) {
                                processedData.incharge_by_backend_user_id = found.id;
                                logger.log('✅ unified-field-mapper: COMPLETE_USER_LIST 兜底映射成功', 'info', {
                                    email: email,
                                    matchedUserId: found.id,
                                    matchedUserName: found.name
                                });
                            } else {
                                logger.log('❌ unified-field-mapper: COMPLETE_USER_LIST 中未找到匹配用户', 'warning', {
                                    email: email,
                                    searchedEmail: email.toLowerCase(),
                                    availableEmails: list.slice(0, 10).map(u => u.email)
                                });
                            }
                        } else {
                            logger.log('❌ unified-field-mapper: 无法获取当前用户邮箱', 'warning', {
                                appStateExists: !!window.getAppState,
                                authUser: window.getAppState?.()?.get('auth.user')
                            });
                        }
                    } else {
                        logger.log('✅ unified-field-mapper: ApiService.getDefaultBackendUserId() 已成功设置负责人ID', 'info', {
                            backendUserId: processedData.incharge_by_backend_user_id
                        });
                    }
                } catch (err) {
                    const logger = getLogger();
                    logger.log('❌ unified-field-mapper: 负责人ID映射过程中发生异常', 'error', {
                        error: err.message,
                        stack: err.stack,
                        timestamp: new Date().toISOString()
                    });
                }
                
                if (!processedData.incharge_by_backend_user_id) {
                    getLogger().log('🚨 unified-field-mapper: 所有映射方法失败，使用最终兜底值', 'error', {
                        finalValue: 1,
                        timestamp: new Date().toISOString()
                    });
                    processedData.incharge_by_backend_user_id = 1; // 最终兜底
                } else {
                    getLogger().log('✅ unified-field-mapper: 负责人ID映射完成', 'info', {
                        finalValue: processedData.incharge_by_backend_user_id,
                        timestamp: new Date().toISOString()
                    });
                }
            }

            this.logger?.log('✅ 数据预处理完成', 'info', {
                inputFields: Object.keys(data).length,
                outputFields: Object.keys(processedData).length,
                hasRequiredFields: {
                    incharge_by_backend_user_id: !!processedData.incharge_by_backend_user_id,
                    languages_id_array: !!processedData.languages_id_array,
                    sub_category_id: !!processedData.sub_category_id,
                    car_type_id: !!processedData.car_type_id
                },
                languages_id_array_format: processedData.languages_id_array ? 
                    (Array.isArray(processedData.languages_id_array) ? 'array' : 'object') : 'null'
            });

            console.log('🚀 unified-field-mapper.processData 最终返回数据', processedData);
            return processedData;
        }



        /**
         * 数据类型转换
         * @param {object} data - 数据对象
         */
        convertDataTypes(data) {
            // 数值字段转换
            const numericFields = ['passenger_number', 'luggage_number', 'ota_price', 'driver_fee', 'sub_category_id', 'car_type_id', 'driving_region_id'];
            numericFields.forEach(field => {
                if (data[field] !== undefined && data[field] !== null) {
                    const numValue = parseFloat(data[field]);
                    if (!isNaN(numValue)) {
                        data[field] = numValue;
                    }
                }
            });

            // 整数字段转换
            const integerFields = ['passenger_number', 'luggage_number', 'sub_category_id', 'car_type_id', 'driving_region_id'];
            integerFields.forEach(field => {
                if (data[field] !== undefined && data[field] !== null) {
                    const intValue = parseInt(data[field]);
                    if (!isNaN(intValue)) {
                        data[field] = intValue;
                    }
                }
            });
        }

        /**
         * 应用默认值
         * @param {object} data - 数据对象
         */
        applyDefaultValues(data) {
            Object.entries(this.DEFAULT_VALUES).forEach(([field, defaultValue]) => {
                if (data[field] === undefined || data[field] === null || data[field] === '') {
                    data[field] = defaultValue;
                    this.logger?.log(`🔧 设置默认值: ${field} = ${defaultValue}`, 'info');
                }
            });
        }

        /**
         * 验证必填字段
         * @param {object} apiData - API格式数据
         * @returns {object} 验证结果
         */
        validateRequiredFields(apiData) {
            const missingFields = this.REQUIRED_API_FIELDS.filter(field => 
                !apiData.hasOwnProperty(field) || 
                apiData[field] === null || 
                apiData[field] === undefined || 
                apiData[field] === ''
            );

            return {
                isValid: missingFields.length === 0,
                missingFields: missingFields,
                message: missingFields.length > 0 ? `缺少必填字段: ${missingFields.join(', ')}` : '验证通过'
            };
        }

        /**
         * 获取日志服务
         * @returns {object} 日志服务
         */
        getLogger() {
            return window.getLogger?.() || window.OTA?.logger || {
                log: console.log.bind(console),
                logError: console.error.bind(console)
            };
        }
    }

    // 注册到全局命名空间
    window.OTA = window.OTA || {};
    window.OTA.UnifiedFieldMapper = UnifiedFieldMapper;

    // 创建全局实例
    window.OTA.unifiedFieldMapper = new UnifiedFieldMapper();

    console.log('✅ 统一字段映射服务已加载', {
        version: '1.0.0',
        strategy: '统一数据契约 + 简化架构',
        features: ['前端↔API映射', '时间字段优先级', '数据类型转换', '默认值设置', '必填字段验证'],
        defaultValues: Object.keys(window.OTA.unifiedFieldMapper.DEFAULT_VALUES).length
    });
    
    // 🔍 验证实例是否正确创建
    if (window.OTA.unifiedFieldMapper && typeof window.OTA.unifiedFieldMapper.processData === 'function') {
        console.log('🔍 unified-field-mapper 实例验证成功', {
            hasProcessData: typeof window.OTA.unifiedFieldMapper.processData === 'function',
            hasConvertDataTypes: typeof window.OTA.unifiedFieldMapper.convertDataTypes === 'function',
            hasApplyDefaultValues: typeof window.OTA.unifiedFieldMapper.applyDefaultValues === 'function'
        });
    } else {
        console.error('🚨 unified-field-mapper 实例创建失败', {
            unifiedFieldMapper: window.OTA.unifiedFieldMapper,
            processData: window.OTA.unifiedFieldMapper?.processData
        });
    }

})();
