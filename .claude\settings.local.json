{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\services/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\services/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js\\managers/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH\\js/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH/**)", "Read(/C:\\Users\\<USER>\\Downloads\\live 1.0 create job GMH/**)", "Bash(rm:*)", "Bash(npm run version:sync:*)", "Bash(npm run validate:*)", "Bash(npm run build:*)", "Bash(find:*)"], "deny": [], "ask": []}}