/**
 * 多订单模式修复测试脚本
 * 在浏览器控制台中运行此脚本来测试修复效果
 */

console.log('🔧 开始多订单模式修复测试');

// 测试1: 检查所有必需组件是否已加载
function testComponentsLoaded() {
    console.log('\n📦 测试1: 检查组件加载状态');
    
    const checks = {
        'OTA命名空间': !!window.OTA,
        'Services命名空间': !!(window.OTA?.Services),
        'Components命名空间': !!(window.OTA?.Components),
        'Pages命名空间': !!(window.OTA?.Pages),
        'StateManager': !!(window.OTA?.Services?.StateManager),
        'OrderCard': !!(window.OTA?.Components?.OrderCard),
        'BatchControls': !!(window.OTA?.Components?.BatchControls),
        'ProgressIndicator': !!(window.OTA?.Components?.ProgressIndicator),
        'StatusPanel': !!(window.OTA?.Components?.StatusPanel),
        'MultiOrderPageV2': !!(window.OTA?.Pages?.MultiOrderPageV2),
        'multiOrderPage实例': !!(window.OTA?.multiOrderPage)
    };
    
    let passed = 0;
    for (const [name, result] of Object.entries(checks)) {
        const status = result ? '✅' : '❌';
        console.log(`${status} ${name}: ${result ? '已加载' : '未找到'}`);
        if (result) passed++;
    }
    
    console.log(`\n📊 结果: ${passed}/${Object.keys(checks).length} 项通过`);
    return passed === Object.keys(checks).length;
}

// 测试2: 创建示例订单数据并触发多订单模式
function testMultiOrderTrigger() {
    console.log('\n🚀 测试2: 手动触发多订单模式');
    
    const sampleOrders = [
        {
            id: 1,
            customer_name: "测试客户1",
            customer_contact: "+60123456789",
            customer_email: "<EMAIL>",
            pickup: "吉隆坡国际机场",
            destination: "市中心酒店",
            date: "2025-08-15",
            time: "14:00",
            passenger_number: 2,
            sub_category_id: 2,
            driving_region_id: 1
        },
        {
            id: 2,
            customer_name: "测试客户2", 
            customer_contact: "+60123456790",
            customer_email: "<EMAIL>",
            pickup: "市中心",
            destination: "购物中心",
            date: "2025-08-15",
            time: "16:30",
            passenger_number: 1,
            sub_category_id: 2,
            driving_region_id: 1
        }
    ];
    
    try {
        // 检查路由系统
        if (window.OTA?.router) {
            console.log('✅ 路由系统可用');
            
            // 尝试导航到多订单页面
            window.OTA.router.navigate('/multi-order', {
                orders: sampleOrders,
                originalText: "测试多订单文本",
                confidence: 1.0,
                detectionMethod: "manual"
            });
            
            console.log('✅ 已触发多订单页面导航');
            return true;
        } else {
            console.log('❌ 路由系统不可用');
            return false;
        }
    } catch (error) {
        console.error('❌ 触发多订单模式失败:', error);
        return false;
    }
}

// 测试3: 直接测试 MultiOrderPageV2
function testMultiOrderPageV2() {
    console.log('\n🔧 测试3: 直接测试 MultiOrderPageV2');
    
    if (!window.OTA?.multiOrderPage) {
        console.log('❌ MultiOrderPageV2 实例不可用');
        return false;
    }
    
    try {
        const testData = {
            orders: [
                {
                    customer_name: "直接测试客户",
                    pickup: "测试起点",
                    destination: "测试终点",
                    date: "2025-08-15",
                    time: "12:00"
                }
            ],
            originalText: "直接测试多订单",
            confidence: 1.0
        };
        
        // 直接调用show方法
        window.OTA.multiOrderPage.show(testData);
        console.log('✅ 直接调用 MultiOrderPageV2.show() 成功');
        return true;
    } catch (error) {
        console.error('❌ 直接测试失败:', error);
        return false;
    }
}

// 测试4: 检查DOM元素状态
function testDOMElements() {
    console.log('\n🏠 测试4: 检查DOM元素状态');
    
    const elements = {
        'multiOrderPanel': document.getElementById('multiOrderPanel'),
        'multiOrderList': document.getElementById('multiOrderList'),
        'batchOtaSelect': document.getElementById('batchOtaSelect'),
        'workspace': document.getElementById('workspace')
    };
    
    for (const [name, element] of Object.entries(elements)) {
        const status = element ? '✅' : '❌';
        console.log(`${status} ${name}: ${element ? '存在' : '不存在'}`);
    }
    
    return Object.values(elements).every(el => !!el);
}

// 主测试函数
async function runAllTests() {
    console.log('🎯 多订单模式综合测试开始\n');
    
    const results = {
        components: testComponentsLoaded(),
        dom: testDOMElements(),
        pageV2: testMultiOrderPageV2(),
        trigger: testMultiOrderTrigger()
    };
    
    console.log('\n📋 测试总结:');
    console.log(`✅ 组件加载: ${results.components ? '通过' : '失败'}`);
    console.log(`✅ DOM元素: ${results.dom ? '通过' : '失败'}`);
    console.log(`✅ 页面V2: ${results.pageV2 ? '通过' : '失败'}`);
    console.log(`✅ 触发测试: ${results.trigger ? '通过' : '失败'}`);
    
    const passedTests = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎉 总体结果: ${passedTests}/${totalTests} 项测试通过`);
    
    if (passedTests === totalTests) {
        console.log('🎊 恭喜！多订单模式修复成功！');
    } else {
        console.log('⚠️ 仍有问题需要解决');
    }
    
    return results;
}

// 辅助函数：显示多订单面板
function showMultiOrderPanel() {
    console.log('👁️ 显示多订单面板...');
    const panel = document.getElementById('multiOrderPanel');
    if (panel) {
        panel.classList.remove('hidden');
        console.log('✅ 多订单面板已显示');
    } else {
        console.log('❌ 找不到多订单面板');
    }
}

// 辅助函数：隐藏多订单面板
function hideMultiOrderPanel() {
    console.log('🙈 隐藏多订单面板...');
    const panel = document.getElementById('multiOrderPanel');
    if (panel) {
        panel.classList.add('hidden');
        console.log('✅ 多订单面板已隐藏');
    } else {
        console.log('❌ 找不到多订单面板');
    }
}

// 导出函数供控制台使用
window.multiOrderFixTest = {
    runAllTests,
    testComponentsLoaded,
    testMultiOrderTrigger,
    testMultiOrderPageV2,
    testDOMElements,
    showMultiOrderPanel,
    hideMultiOrderPanel
};

console.log('🛠️ 多订单修复测试工具已加载');
console.log('📖 使用方法:');
console.log('  multiOrderFixTest.runAllTests() - 运行所有测试');
console.log('  multiOrderFixTest.showMultiOrderPanel() - 显示多订单面板');
console.log('  multiOrderFixTest.hideMultiOrderPanel() - 隐藏多订单面板');
console.log('  multiOrderFixTest.testMultiOrderTrigger() - 测试多订单触发');

// 自动运行测试（延迟执行，确保所有脚本已加载）
setTimeout(() => {
    console.log('\n🚀 自动运行修复测试...');
    runAllTests();
}, 1000);