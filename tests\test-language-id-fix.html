<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言ID修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔧 语言ID映射修复测试</h1>
    
    <div class="test-section">
        <h3>测试场景1: 中文订单</h3>
        <textarea id="chineseOrder" rows="3" cols="50">接机服务从吉隆坡机场到双子塔酒店</textarea>
        <button onclick="testChineseDetection()">测试中文检测</button>
        <div id="chineseResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试场景2: 英文订单</h3>
        <textarea id="englishOrder" rows="3" cols="50">Airport pickup from KLIA to Twin Towers Hotel</textarea>
        <button onclick="testEnglishDetection()">测试英文检测</button>
        <div id="englishResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>语言ID标准对照</h3>
        <ul>
            <li>ID=2: English ✅</li>
            <li>ID=3: Malay ✅</li>
            <li>ID=4: Chinese ✅ (修复后使用)</li>
            <li>ID=5: Paging ✅</li>
            <li>ID=6: Charter ✅</li>
            <li><strong style="color: red;">ID=1: 不存在 ❌ (之前错误使用)</strong></li>
        </ul>
    </div>

    <script>
        // 模拟FormManager的detectLanguageFromContent方法
        function detectLanguageFromContent(content) {
            // 简单的中文检测：包含中文字符就是中文，否则英文
            const hasChinese = /[\u4e00-\u9fa5]/.test(content);

            return hasChinese
                ? { "0": "4" }  // 中文 (修复: 使用正确的language_id=4)
                : { "0": "2" }; // 英文
        }

        function testChineseDetection() {
            const content = document.getElementById('chineseOrder').value;
            const result = detectLanguageFromContent(content);
            const resultDiv = document.getElementById('chineseResult');
            
            const isCorrect = result["0"] === "4";
            resultDiv.className = `result ${isCorrect ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <strong>输入内容:</strong> ${content}<br>
                <strong>检测结果:</strong> ${JSON.stringify(result)}<br>
                <strong>语言ID:</strong> ${result["0"]} (${result["0"] === "4" ? "Chinese ✅" : "错误 ❌"})<br>
                <strong>测试状态:</strong> ${isCorrect ? "✅ 修复成功" : "❌ 仍有问题"}
            `;
        }

        function testEnglishDetection() {
            const content = document.getElementById('englishOrder').value;
            const result = detectLanguageFromContent(content);
            const resultDiv = document.getElementById('englishResult');
            
            const isCorrect = result["0"] === "2";
            resultDiv.className = `result ${isCorrect ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <strong>输入内容:</strong> ${content}<br>
                <strong>检测结果:</strong> ${JSON.stringify(result)}<br>
                <strong>语言ID:</strong> ${result["0"]} (${result["0"] === "2" ? "English ✅" : "错误 ❌"})<br>
                <strong>测试状态:</strong> ${isCorrect ? "✅ 正常工作" : "❌ 仍有问题"}
            `;
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            console.log('🧪 语言ID修复测试页面已加载');
            console.log('修复前: 中文订单错误返回 {"0": "1"}，导致API错误');
            console.log('修复后: 中文订单正确返回 {"0": "4"}，符合API标准');
        };
    </script>
</body>
</html>