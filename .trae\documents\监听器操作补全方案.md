# 监听器操作补全方案

## 1. 项目监听器现状分析

### 1.1 监听器分类清单

#### 全局事件监听器 (GlobalEventCoordinator)
- **文件位置**: `js/core/global-event-coordinator.js`
- **已实现监听器**:
  - `click` - 全局点击事件处理
  - `keydown` - 全局键盘事件处理
  - `resize` - 窗口大小变化
  - `scroll` - 页面滚动事件
- **缺失操作**: 部分键盘快捷键组合、触摸事件、拖拽事件

#### UI管理器监听器 (UIManager)
- **文件位置**: `js/ui-manager.js`
- **已实现监听器**:
  - `resize` - 窗口大小变化响应
  - `visibilitychange` - 页面可见性变化
- **缺失操作**: 表单验证事件、模态框交互事件、响应式布局调整

#### 事件管理器 (EventManager)
- **文件位置**: `js/managers/event-manager.js`
- **已实现监听器**:
  - 按钮点击事件（登录、登出、解析、创建等）
  - 键盘快捷键（Ctrl+Enter、Escape等）
  - 表单字段编辑事件
- **缺失操作**: 实时表单验证、自动保存、拖拽上传

#### 多订单状态管理器 (MultiOrderStateManager)
- **文件位置**: `js/multi-order/multi-order-state-manager.js`
- **已实现监听器**:
  - `addListener` - 状态监听器添加
  - `removeListener` - 状态监听器移除
  - `notifyListeners` - 监听器通知机制
- **缺失操作**: 状态变化的具体业务逻辑处理

#### 多订单协调器 (MultiOrderCoordinator)
- **文件位置**: `js/multi-order/multi-order-coordinator.js`
- **已实现监听器**:
  - `addEventListener` - 事件监听器添加
  - `dispatchEvent` - 事件分发
- **缺失操作**: 批量操作进度监听、订单状态同步

#### 基础组件监听器 (BaseComponent)
- **文件位置**: `js/pages/multi-order/components/base-component.js`
- **已实现监听器**:
  - `addEventListener` - 组件级事件监听
  - `removeAllEventListeners` - 事件监听器清理
- **缺失操作**: 组件生命周期事件、数据绑定更新

## 2. 缺失监听器操作识别

### 2.1 高优先级缺失操作

#### 表单实时验证监听器
```javascript
// 缺失：实时表单验证
class FormValidationListener {
    constructor() {
        this.validationRules = new Map();
        this.errorDisplays = new Map();
    }
    
    // 需要实现：字段失焦验证
    onFieldBlur(fieldName, value) {
        // 实现字段级验证逻辑
    }
    
    // 需要实现：实时输入验证
    onFieldInput(fieldName, value) {
        // 实现实时验证反馈
    }
}
```

#### 自动保存监听器
```javascript
// 缺失：表单自动保存
class AutoSaveListener {
    constructor() {
        this.saveTimer = null;
        this.isDirty = false;
    }
    
    // 需要实现：表单变化监听
    onFormChange(formData) {
        // 实现自动保存逻辑
    }
    
    // 需要实现：定时保存
    scheduleAutoSave() {
        // 实现定时保存机制
    }
}
```

#### 拖拽上传监听器
```javascript
// 缺失：文件拖拽上传
class DragDropListener {
    constructor() {
        this.dropZones = new Set();
    }
    
    // 需要实现：拖拽进入
    onDragEnter(event) {
        // 实现拖拽视觉反馈
    }
    
    // 需要实现：文件放置
    onDrop(event) {
        // 实现文件处理逻辑
    }
}
```

### 2.2 中优先级缺失操作

#### 状态同步监听器
```javascript
// 缺失：多组件状态同步
class StateSyncListener {
    constructor() {
        this.syncTargets = new Map();
    }
    
    // 需要实现：状态变化广播
    onStateChange(statePath, newValue, oldValue) {
        // 实现状态同步逻辑
    }
}
```

#### 网络状态监听器
```javascript
// 缺失：网络连接状态监听
class NetworkStatusListener {
    constructor() {
        this.isOnline = navigator.onLine;
    }
    
    // 需要实现：网络状态变化
    onNetworkChange(isOnline) {
        // 实现离线/在线状态处理
    }
}
```

## 3. 监听器实现方案

### 3.1 表单实时验证实现

```javascript
/**
 * 表单实时验证监听器实现
 * @LISTENER @FORM_VALIDATION
 */
class FormValidationListener {
    constructor(formManager) {
        this.formManager = formManager;
        this.validationRules = this.initValidationRules();
        this.debounceTimers = new Map();
        this.setupListeners();
    }
    
    /**
     * 初始化验证规则
     */
    initValidationRules() {
        return {
            customerName: {
                required: true,
                minLength: 2,
                pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/
            },
            contactNumber: {
                required: true,
                pattern: /^[\+]?[1-9][\d]{0,3}[\s]?[\d]{4,14}$/
            },
            email: {
                required: false,
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            },
            pickupLocation: {
                required: true,
                minLength: 5
            },
            destination: {
                required: true,
                minLength: 5
            }
        };
    }
    
    /**
     * 设置表单监听器
     */
    setupListeners() {
        const form = document.getElementById('orderForm');
        if (!form) return;
        
        // 监听所有输入字段
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            // 实时输入验证（防抖）
            input.addEventListener('input', (e) => {
                this.debounceValidation(e.target.name, e.target.value, 300);
            });
            
            // 失焦验证（立即）
            input.addEventListener('blur', (e) => {
                this.validateField(e.target.name, e.target.value, true);
            });
            
            // 获得焦点时清除错误
            input.addEventListener('focus', (e) => {
                this.clearFieldError(e.target.name);
            });
        });
    }
    
    /**
     * 防抖验证
     */
    debounceValidation(fieldName, value, delay) {
        if (this.debounceTimers.has(fieldName)) {
            clearTimeout(this.debounceTimers.get(fieldName));
        }
        
        const timer = setTimeout(() => {
            this.validateField(fieldName, value, false);
        }, delay);
        
        this.debounceTimers.set(fieldName, timer);
    }
    
    /**
     * 字段验证
     */
    validateField(fieldName, value, showSuccess = false) {
        const rules = this.validationRules[fieldName];
        if (!rules) return true;
        
        const errors = [];
        
        // 必填验证
        if (rules.required && (!value || value.trim() === '')) {
            errors.push(`${this.getFieldLabel(fieldName)}为必填项`);
        }
        
        // 长度验证
        if (value && rules.minLength && value.length < rules.minLength) {
            errors.push(`${this.getFieldLabel(fieldName)}至少需要${rules.minLength}个字符`);
        }
        
        // 格式验证
        if (value && rules.pattern && !rules.pattern.test(value)) {
            errors.push(`${this.getFieldLabel(fieldName)}格式不正确`);
        }
        
        // 显示验证结果
        if (errors.length > 0) {
            this.showFieldError(fieldName, errors[0]);
            return false;
        } else {
            this.clearFieldError(fieldName);
            if (showSuccess && value) {
                this.showFieldSuccess(fieldName);
            }
            return true;
        }
    }
    
    /**
     * 显示字段错误
     */
    showFieldError(fieldName, message) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;
        
        // 添加错误样式
        field.classList.add('error');
        field.classList.remove('success');
        
        // 显示错误消息
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            field.parentNode.appendChild(errorElement);
        }
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }
    
    /**
     * 显示字段成功状态
     */
    showFieldSuccess(fieldName) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;
        
        field.classList.add('success');
        field.classList.remove('error');
    }
    
    /**
     * 清除字段错误
     */
    clearFieldError(fieldName) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;
        
        field.classList.remove('error', 'success');
        
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }
    
    /**
     * 获取字段标签
     */
    getFieldLabel(fieldName) {
        const labelMap = {
            customerName: '客户姓名',
            contactNumber: '联系电话',
            email: '邮箱地址',
            pickupLocation: '取货地点',
            destination: '目的地'
        };
        return labelMap[fieldName] || fieldName;
    }
    
    /**
     * 验证整个表单
     */
    validateForm() {
        const form = document.getElementById('orderForm');
        if (!form) return false;
        
        let isValid = true;
        const formData = new FormData(form);
        
        for (const [fieldName] of formData.entries()) {
            if (!this.validateField(fieldName, formData.get(fieldName), true)) {
                isValid = false;
            }
        }
        
        return isValid;
    }
}
```

### 3.2 自动保存监听器实现

```javascript
/**
 * 自动保存监听器实现
 * @LISTENER @AUTO_SAVE
 */
class AutoSaveListener {
    constructor(formManager, stateManager) {
        this.formManager = formManager;
        this.stateManager = stateManager;
        this.saveTimer = null;
        this.isDirty = false;
        this.lastSaveData = null;
        this.saveInterval = 30000; // 30秒自动保存
        this.setupListeners();
    }
    
    /**
     * 设置自动保存监听器
     */
    setupListeners() {
        const form = document.getElementById('orderForm');
        if (!form) return;
        
        // 监听表单变化
        form.addEventListener('input', (e) => {
            this.onFormChange();
        });
        
        form.addEventListener('change', (e) => {
            this.onFormChange();
        });
        
        // 页面卸载前保存
        window.addEventListener('beforeunload', (e) => {
            if (this.isDirty) {
                this.saveFormData();
            }
        });
        
        // 页面可见性变化时保存
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.isDirty) {
                this.saveFormData();
            }
        });
    }
    
    /**
     * 表单变化处理
     */
    onFormChange() {
        this.isDirty = true;
        this.scheduleAutoSave();
        this.showUnsavedIndicator();
    }
    
    /**
     * 计划自动保存
     */
    scheduleAutoSave() {
        if (this.saveTimer) {
            clearTimeout(this.saveTimer);
        }
        
        this.saveTimer = setTimeout(() => {
            this.saveFormData();
        }, this.saveInterval);
    }
    
    /**
     * 保存表单数据
     */
    async saveFormData() {
        try {
            const formData = this.formManager.collectFormData();
            
            // 检查数据是否有变化
            if (this.isDataUnchanged(formData)) {
                return;
            }
            
            // 保存到本地存储
            const saveData = {
                formData,
                timestamp: Date.now(),
                version: this.generateVersion()
            };
            
            localStorage.setItem('autoSave_orderForm', JSON.stringify(saveData));
            
            this.lastSaveData = formData;
            this.isDirty = false;
            this.hideUnsavedIndicator();
            this.showSaveSuccess();
            
            // 记录日志
            if (typeof getLogger === 'function') {
                getLogger().log('表单数据已自动保存', 'info');
            }
            
        } catch (error) {
            console.error('自动保存失败:', error);
            this.showSaveError();
        }
    }
    
    /**
     * 检查数据是否未变化
     */
    isDataUnchanged(currentData) {
        if (!this.lastSaveData) return false;
        
        return JSON.stringify(currentData) === JSON.stringify(this.lastSaveData);
    }
    
    /**
     * 生成数据版本号
     */
    generateVersion() {
        return `v${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 恢复保存的数据
     */
    restoreSavedData() {
        try {
            const savedData = localStorage.getItem('autoSave_orderForm');
            if (!savedData) return false;
            
            const { formData, timestamp } = JSON.parse(savedData);
            
            // 检查数据是否过期（24小时）
            if (Date.now() - timestamp > 24 * 60 * 60 * 1000) {
                localStorage.removeItem('autoSave_orderForm');
                return false;
            }
            
            // 询问用户是否恢复
            if (this.confirmRestore(timestamp)) {
                this.formManager.fillFormFromData(formData);
                this.lastSaveData = formData;
                this.showRestoreSuccess();
                return true;
            }
            
        } catch (error) {
            console.error('恢复保存数据失败:', error);
        }
        
        return false;
    }
    
    /**
     * 确认恢复对话框
     */
    confirmRestore(timestamp) {
        const saveTime = new Date(timestamp).toLocaleString();
        return confirm(`发现自动保存的表单数据（保存时间：${saveTime}），是否恢复？`);
    }
    
    /**
     * 显示未保存指示器
     */
    showUnsavedIndicator() {
        let indicator = document.getElementById('unsavedIndicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'unsavedIndicator';
            indicator.className = 'unsaved-indicator';
            indicator.innerHTML = '● 有未保存的更改';
            document.body.appendChild(indicator);
        }
        indicator.style.display = 'block';
    }
    
    /**
     * 隐藏未保存指示器
     */
    hideUnsavedIndicator() {
        const indicator = document.getElementById('unsavedIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
    
    /**
     * 显示保存成功提示
     */
    showSaveSuccess() {
        this.showToast('✓ 已自动保存', 'success');
    }
    
    /**
     * 显示保存错误提示
     */
    showSaveError() {
        this.showToast('✗ 自动保存失败', 'error');
    }
    
    /**
     * 显示恢复成功提示
     */
    showRestoreSuccess() {
        this.showToast('✓ 已恢复保存的数据', 'success');
    }
    
    /**
     * 显示提示消息
     */
    showToast(message, type) {
        // 使用现有的提示系统
        if (window.OTA?.managers?.ui?.showQuickToast) {
            window.OTA.managers.ui.showQuickToast(message, type);
        } else {
            console.log(`[AutoSave] ${message}`);
        }
    }
    
    /**
     * 清除自动保存数据
     */
    clearSavedData() {
        localStorage.removeItem('autoSave_orderForm');
        this.lastSaveData = null;
        this.isDirty = false;
        this.hideUnsavedIndicator();
    }
    
    /**
     * 销毁监听器
     */
    destroy() {
        if (this.saveTimer) {
            clearTimeout(this.saveTimer);
        }
        this.clearSavedData();
    }
}
```

### 3.3 拖拽上传监听器实现

```javascript
/**
 * 拖拽上传监听器实现
 * @LISTENER @DRAG_DROP_UPLOAD
 */
class DragDropUploadListener {
    constructor(uploadManager) {
        this.uploadManager = uploadManager;
        this.dropZones = new Set();
        this.isDragging = false;
        this.setupGlobalListeners();
    }
    
    /**
     * 设置全局拖拽监听器
     */
    setupGlobalListeners() {
        // 防止默认拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            }, false);
        });
        
        // 全局拖拽状态管理
        document.addEventListener('dragenter', (e) => {
            this.onGlobalDragEnter(e);
        });
        
        document.addEventListener('dragleave', (e) => {
            this.onGlobalDragLeave(e);
        });
        
        document.addEventListener('drop', (e) => {
            this.onGlobalDrop(e);
        });
    }
    
    /**
     * 注册拖拽区域
     */
    registerDropZone(element, options = {}) {
        if (!element) return;
        
        const dropZone = {
            element,
            options: {
                acceptedTypes: options.acceptedTypes || ['image/*', '.pdf', '.doc', '.docx'],
                maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 10MB
                multiple: options.multiple || false,
                onDrop: options.onDrop || this.defaultDropHandler.bind(this),
                onDragEnter: options.onDragEnter,
                onDragLeave: options.onDragLeave
            }
        };
        
        this.dropZones.add(dropZone);
        this.setupDropZoneListeners(dropZone);
        
        return dropZone;
    }
    
    /**
     * 设置拖拽区域监听器
     */
    setupDropZoneListeners(dropZone) {
        const { element, options } = dropZone;
        
        element.addEventListener('dragenter', (e) => {
            e.preventDefault();
            this.onDropZoneDragEnter(dropZone, e);
        });
        
        element.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';
        });
        
        element.addEventListener('dragleave', (e) => {
            e.preventDefault();
            this.onDropZoneDragLeave(dropZone, e);
        });
        
        element.addEventListener('drop', (e) => {
            e.preventDefault();
            this.onDropZoneDrop(dropZone, e);
        });
    }
    
    /**
     * 全局拖拽进入
     */
    onGlobalDragEnter(e) {
        if (!this.isDragging) {
            this.isDragging = true;
            this.showDropOverlay();
        }
    }
    
    /**
     * 全局拖拽离开
     */
    onGlobalDragLeave(e) {
        // 检查是否真的离开了窗口
        if (e.clientX === 0 && e.clientY === 0) {
            this.isDragging = false;
            this.hideDropOverlay();
        }
    }
    
    /**
     * 全局拖拽放置
     */
    onGlobalDrop(e) {
        this.isDragging = false;
        this.hideDropOverlay();
    }
    
    /**
     * 拖拽区域进入
     */
    onDropZoneDragEnter(dropZone, e) {
        const { element, options } = dropZone;
        
        element.classList.add('drag-over');
        
        if (options.onDragEnter) {
            options.onDragEnter(e);
        }
    }
    
    /**
     * 拖拽区域离开
     */
    onDropZoneDragLeave(dropZone, e) {
        const { element, options } = dropZone;
        
        // 检查是否真的离开了拖拽区域
        if (!element.contains(e.relatedTarget)) {
            element.classList.remove('drag-over');
            
            if (options.onDragLeave) {
                options.onDragLeave(e);
            }
        }
    }
    
    /**
     * 拖拽区域放置
     */
    onDropZoneDrop(dropZone, e) {
        const { element, options } = dropZone;
        
        element.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files);
        
        if (files.length === 0) return;
        
        // 验证文件
        const validFiles = this.validateFiles(files, options);
        
        if (validFiles.length > 0) {
            options.onDrop(validFiles, e);
        }
    }
    
    /**
     * 验证文件
     */
    validateFiles(files, options) {
        const validFiles = [];
        const errors = [];
        
        for (const file of files) {
            // 检查文件类型
            if (!this.isFileTypeAccepted(file, options.acceptedTypes)) {
                errors.push(`文件 "${file.name}" 类型不支持`);
                continue;
            }
            
            // 检查文件大小
            if (file.size > options.maxFileSize) {
                errors.push(`文件 "${file.name}" 超过大小限制`);
                continue;
            }
            
            validFiles.push(file);
        }
        
        // 检查文件数量
        if (!options.multiple && validFiles.length > 1) {
            errors.push('只能上传一个文件');
            return [];
        }
        
        // 显示错误信息
        if (errors.length > 0) {
            this.showValidationErrors(errors);
        }
        
        return validFiles;
    }
    
    /**
     * 检查文件类型是否被接受
     */
    isFileTypeAccepted(file, acceptedTypes) {
        return acceptedTypes.some(type => {
            if (type.startsWith('.')) {
                return file.name.toLowerCase().endsWith(type.toLowerCase());
            } else if (type.includes('*')) {
                const [mainType] = type.split('/');
                return file.type.startsWith(mainType);
            } else {
                return file.type === type;
            }
        });
    }
    
    /**
     * 默认放置处理器
     */
    defaultDropHandler(files, event) {
        console.log('文件拖拽上传:', files);
        
        // 使用现有的上传管理器
        if (this.uploadManager && this.uploadManager.handleFileUpload) {
            files.forEach(file => {
                this.uploadManager.handleFileUpload(file);
            });
        }
    }
    
    /**
     * 显示拖拽覆盖层
     */
    showDropOverlay() {
        let overlay = document.getElementById('dragDropOverlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'dragDropOverlay';
            overlay.className = 'drag-drop-overlay';
            overlay.innerHTML = `
                <div class="drag-drop-message">
                    <div class="drag-drop-icon">📁</div>
                    <div class="drag-drop-text">拖拽文件到此处上传</div>
                </div>
            `;
            document.body.appendChild(overlay);
        }
        overlay.style.display = 'flex';
    }
    
    /**
     * 隐藏拖拽覆盖层
     */
    hideDropOverlay() {
        const overlay = document.getElementById('dragDropOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
    
    /**
     * 显示验证错误
     */
    showValidationErrors(errors) {
        const message = errors.join('\n');
        
        if (window.OTA?.managers?.ui?.showAlert) {
            window.OTA.managers.ui.showAlert(message, 'error');
        } else {
            alert(message);
        }
    }
    
    /**
     * 注销拖拽区域
     */
    unregisterDropZone(element) {
        for (const dropZone of this.dropZones) {
            if (dropZone.element === element) {
                this.dropZones.delete(dropZone);
                break;
            }
        }
    }
    
    /**
     * 销毁监听器
     */
    destroy() {
        this.dropZones.clear();
        this.hideDropOverlay();
        
        const overlay = document.getElementById('dragDropOverlay');
        if (overlay) {
            overlay.remove();
        }
    }
}
```

## 4. 监听器依赖关系和协调机制

### 4.1 依赖关系图

```mermaid
graph TD
    A[GlobalEventCoordinator] --> B[UIManager]
    A --> C[EventManager]
    B --> D[FormValidationListener]
    B --> E[AutoSaveListener]
    C --> F[DragDropUploadListener]
    G[MultiOrderCoordinator] --> H[MultiOrderStateManager]
    H --> I[StateSyncListener]
    J[BaseComponent] --> K[ComponentLifecycleListener]
    
    style A fill:#ff9999
    style G fill:#99ccff
    style J fill:#99ff99
```

### 4.2 事件流程和优先级

#### 事件处理优先级（从高到低）
1. **系统级事件** - 页面卸载、网络状态变化
2. **安全级事件** - 用户认证、权限验证
3. **业务级事件** - 表单提交、数据保存
4. **交互级事件** - 用户输入、界面响应
5. **辅助级事件** - 自