/**
 * 国际化管理器
 * 负责多语言支持和文本翻译
 *
 * @FIELD_FORMAT: camelCase - 待转换为 snake_case
 * @I18N_KEYS_USED: form.customerName, form.customerContact, form.customerEmail, form.flightInfo,
 *                  form.otaPrice, form.otaReferenceNumber, form.pickupDate, form.pickupTime,
 *                  form.passengerCount, form.luggageCount, form.subCategoryId, form.carTypeId,
 *                  form.drivingRegionId, form.extraRequirement
 * @CONVERSION_TARGET: form.customer_name, form.customer_contact, form.customer_email, form.flight_info,
 *                    form.ota_price, form.ota_reference_number, form.date, form.time,
 *                    form.passenger_number, form.luggage_number, form.sub_category_id, form.car_type_id,
 *                    form.driving_region_id, form.extra_requirement
 *
 * <AUTHOR>
 * @version 2.4.2
 */

// 获取依赖模块 - 使用统一的服务定位器

class I18nManager {
    constructor() {
        this.currentLanguage = 'en'; // 修改默认语言为英文
        this.storageKey = 'ota_language_preference';
        this.translations = {};
        // 移除构造函数中的自动初始化，改为由main.js显式调用
        // this.init(); 
    }

    /**
     * 初始化国际化管理器
     */
    init() {
        // 加载语言资源
        this.loadTranslations();
        
        // 优先从AppState加载语言设置，其次从localStorage，最后使用默认值
        let savedLanguage = null;
        
        // 尝试从AppState获取
        if (window.OTA && window.OTA.appState) {
            savedLanguage = window.OTA.appState.get('config.language');
        }
        
        // 如果AppState中没有，从localStorage加载
        if (!savedLanguage) {
            savedLanguage = localStorage.getItem(this.storageKey);
        }
        
        // 设置语言
        if (savedLanguage && this.translations[savedLanguage]) {
            this.currentLanguage = savedLanguage;
        }
        
        // 初始化完成后立即更新UI
        this.updateUI();
        
        const logger = getLogger();
        if (logger) {
            logger.log(`国际化管理器已初始化，当前语言: ${this.currentLanguage}`, 'info');
        }
    }

    /**
     * 加载翻译资源
     */
    loadTranslations() {
        this.translations = {
            zh: {
                // 通用
                'common.loading': '加载中...',
                'common.success': '成功',
                'common.error': '错误',
                'common.warning': '警告',
                'common.info': '信息',
                'common.confirm': '确认',
                'common.cancel': '取消',
                'common.close': '关闭',
                'common.save': '保存',
                'common.delete': '删除',
                'common.edit': '编辑',
                'common.search': '搜索',
                'common.reset': '重置',
                'common.export': '导出',
                'common.clear': '清空',
                'common.copy': '复制',
                'common.view': '查看',
                'common.flightNumber': '查询航班号: {flightNumber}',

                // 头部导航
                'header.pageTitle': 'OTA订单处理系统 - GoMyHire Integration',
                'header.title': 'OTA订单处理系统',
                'header.defaultEmail': '默认邮箱:',
                'header.defaultEmailPlaceholder': '设置默认客户邮箱',
                'header.defaultEmailTooltip': '设置默认客户邮箱，当AI解析无法获取邮箱时自动使用',
                'header.historyOrders': '历史订单',
                'header.logout': '退出登录',
                'header.toggleTheme': '切换主题',
                'header.language': '选择语言',
                'header.languageZh': '中文',
                'header.languageEn': 'English',
                
                // 登录界面
                'login.title': '用户登录',
                'login.email': '邮箱地址',
                'login.password': '密码',
                'login.rememberMe': '记住登录状态',
                'login.loginButton': '登录',
                'login.clearSaved': '清除保存的账号',
                'login.emailPlaceholder': '请输入邮箱地址',
                'login.passwordPlaceholder': '请输入密码',
                
                // 智能输入区
                'input.title': '📝 订单输入',
                'input.orderDescription': '订单描述',
                'input.placeholder': '请输入订单描述文本，系统将自动解析订单信息...',
                'input.clearButton': '清空',
                'input.sampleButton': '示例数据',
                'input.parseButton': '手动解析',
                'input.imageUpload': '图片上传',
                'input.geminiStatus': '请输入订单描述',
                
                // 订单预览
                'preview.title': '📋 订单预览与编辑',
                'preview.validate': '验证数据',
                'preview.reset': '重置',
                'preview.close': '关闭',
                
                // 表单字段
                'form.basicInfo': '基本信息',
                'form.serviceType': '服务类型',
                'form.serviceTypePlaceholder': '请选择服务类型',
                'form.serviceTypeTooltip': '选择服务类型',
                'form.selectServiceType': '请选择服务类型',
                'form.subCategory': '子分类',
                'form.subCategoryPlaceholder': '请选择子分类',
                'form.otaReference': 'OTA参考号',
                'form.otaReferencePlaceholder': 'OTA平台订单号',
                'form.otaReferenceTooltip': 'OTA平台的订单参考号',
                'form.otaChannel': 'OTA 渠道名字',
                'form.otaChannelPlaceholder': '请选择OTA渠道',
                'form.otaChannelTooltip': '选择OTA渠道',
                'form.selectOtaChannel': '请选择OTA渠道',
                'form.otaChannelCustom': '自定义OTA',
                'form.carType': '车型',
                'form.carTypePlaceholder': '请选择车型',
                'form.carTypeTooltip': '选择车型',
                'form.selectCarType': '请选择车型',
                'form.incharge': '负责人',
                'form.inchargePlaceholder': '请选择负责人',
                'form.extraRequirementPlaceholder': '其他特殊要求或备注',
                
                'form.customerInfo': '客户信息',
                'form.customerName': '客户姓名',
                'form.customerNamePlaceholder': '客户姓名',
                'form.customerNameTooltip': '客户的姓名',
                'form.customerPhone': '联系电话',
                'form.customerContact': '联系电话',
                'form.customerContactPlaceholder': '联系电话',
                'form.customerPhonePlaceholder': '联系电话',
                'form.customerPhoneTooltip': '客户的联系电话',
                'form.customerEmail': '客户邮箱',
                'form.customerEmailPlaceholder': '客户邮箱',
                'form.customerEmailTooltip': '客户的邮箱地址',
                'form.flightInfo': '航班信息',
                'form.flightInfoPlaceholder': '航班号/航班信息',
                'form.flightInfoTooltip': '航班号或相关航班信息',
                
                'form.tripInfo': '🚗 行程信息',
                'form.serviceConfig': '⚙️ 服务配置',
                'form.pickup': '上车地点',
                'form.pickupPlaceholder': '上车地点',
                'form.pickupTooltip': '客户上车的地点',
                'form.dropoff': '目的地',
                'form.dropoffPlaceholder': '目的地',
                'form.dropoffTooltip': '客户的目的地',
                'form.destination': '目的地',
                'form.destinationPlaceholder': '目的地',
                'form.pickupDate': '接送日期',
                'form.pickupDateTooltip': '接送日期',
                'form.pickupTime': '接送时间',
                'form.pickupTimeTooltip': '接送时间',
                'form.date': '日期',
                'form.time': '时间',
                'form.passengerCount': '乘客人数',
                'form.passengerCountPlaceholder': '乘客人数',
                'form.passengerCountTooltip': '乘客人数',
                'form.passengerNumber': '乘客人数',
                'form.luggageCount': '行李件数',
                'form.luggageCountPlaceholder': '行李件数',
                'form.luggageCountTooltip': '行李件数',
                'form.luggageNumber': '行李数量',
                'form.specialRequests': '特殊要求',
                'form.specialRequestsPlaceholder': '特殊要求或备注',
                
                'form.additionalInfo': '附加信息',
                'form.priceInfo': '💰 价格信息',
                'form.price': '价格',
                'form.pricePlaceholder': '价格',
                'form.priceTooltip': '订单价格',
                'form.currencyPlaceholder': '选择货币',
                'form.currencyTooltip': '选择货币',
                'form.otaPrice': 'OTA价格',
                'form.otaPricePlaceholder': 'OTA价格',
                'form.otaPriceTooltip': 'OTA订单价格',
                'form.otaReferenceNumber': 'OTA参考号',
                'form.driverFee': '司机费用',
                'form.driverFeePlaceholder': '司机费用',
                'form.driverFeeTooltip': '司机服务费用',
                'form.drivingRegion': '行驶区域',
                'form.drivingRegionPlaceholder': '请选择行驶区域', 
                'form.drivingRegionTooltip': '选择行驶区域',
                'form.selectDrivingRegion': '请选择行驶区域',
                'form.languages': '需求语言',
                'form.languagesPlaceholder': '请选择语言要求',
                'form.languagesTooltip': '选择语言要求',
                'form.selectLanguages': '请选择语言',
                
                // 服务类型
                'serviceType.pickup': '接机服务',
                'serviceType.dropoff': '送机服务',
                'serviceType.charter': '包车服务',
                'serviceType.paging': '举牌服务',
                
                // 车型显示
                'carType.5seater': '5座车',
                'carType.7seaterMpv': '7座MPV',
                'carType.10seaterMpv': '10座MPV',
                'carType.velfire': 'Velfire/Alphard',
                'carType.premium5': '豪华5座车',
                'carType.ticket': '票务服务',
                'carType.7seaterSuv': '7座SUV',
                'carType.extended5': '加长5座',
                'carType.4seaterHatch': '4座掀背车',
                'carType.ticketNonMalaysian': '票务服务(非马来西亚)',
                'carType.14seaterVan': '14座厢型车',
                'carType.18seaterVan': '18座厢型车',
                'carType.30seatBus': '30座小巴',
                'carType.44seaterBus': '44座大巴',
                
                // 区域显示
                'region.klSelangor': '吉隆坡/雪兰莪',
                'region.penang': '槟城',
                'region.johor': '柔佛',
                'region.sabah': '沙巴',
                'region.singapore': '新加坡',
                // 'region.ctrip': '携程专车',
                // 'region.complete': 'Complete',
                // 'region.paging': 'Paging',
                // 'region.charter': 'Charter',
                'region.malacca': '马六甲',
                'region.sarawak': '砂拉越',
                
                // 历史记录字段标签
                'history.customer': '客户',
                'history.contact': '联系电话',
                'history.email': '客户邮箱',
                'history.pickup': '上车地点',
                'history.destination': '目的地',
                'history.date': '日期',
                'history.time': '时间',
                'history.flight': '航班',
                'history.service': '服务',
                'history.carType': '车型',
                'history.region': '驾驶区域',
                'history.passengers': '乘客数量',
                'history.luggage': '行李数量',
                'history.otaReference': 'OTA参考号',
                'history.requirements': '额外要求',
                'history.currency': '货币',
                
                // 复制功能相关
                'form.orderId': '订单ID',
                'form.orderType': '订单类型',
                'copy.basicInfo': '=== 基本订单信息 ===',
                'copy.tripInfo': '=== 行程信息 ===',
                'copy.customerInfo': '=== 客户信息 ===',
                'copy.serviceInfo': '=== 服务详情 ===',
                'copy.extraRequirements': '=== 特殊要求 ===',
                'copy.notProvided': '(未提供)',

                // 特殊要求
                'form.specialRequirements': '特殊要求',
                'form.babyChair': '儿童座椅',
                'form.tourGuide': '导游服务',
                'form.meetAndGreet': '迎接服务',
                'form.extraRequirement': '额外要求',
                'form.extraRequirementTooltip': '其他特殊要求或备注',
                
                // 操作按钮
                'actions.previewOrder': '预览订单',
                'actions.createOrder': '创建订单',
                'actions.resetForm': '重置表单',
                'actions.validateData': '提示数据异常',
                
                // 状态信息
                'status.connected': '已连接',
                'status.disconnected': '未连接',
                'status.ready': '数据已就绪',
                'status.notReady': '数据未就绪',
                'status.waiting': '等待数据',
                
                // 历史订单
                'history.title': '📋 历史订单管理',
                'history.export': '导出',
                'history.clear': '清空',
                'history.searchOrderId': '订单ID',
                'history.searchOrderIdPlaceholder': '搜索订单ID',
                'history.searchCustomer': '客户姓名',
                'history.searchCustomerPlaceholder': '搜索客户姓名',
                'history.searchDateFrom': '开始日期',
                'history.searchDateTo': '结束日期',
                'history.searchButton': '搜索',
                'history.resetSearch': '重置',
                'history.statTotal': '总计',
                'history.statToday': '今日',
                'history.statWeek': '本周',
                'history.statMonth': '本月',
                'history.orderList': '订单列表',
                'history.recordCount': '共 {count} 条记录',
                'history.emptyState': '暂无历史订单',
                'history.viewDetail': '查看详情',
                'history.copyOrder': '复制订单',
                'history.deleteOrder': '删除',
                
                // 提示信息
                'messages.loginSuccess': '登录成功',
                'messages.loginFailed': '登录失败',
                'messages.logoutSuccess': '已成功退出登录',
                'messages.logoutConfirm': '确定要退出登录吗？当前未保存的数据可能丢失。',
                'messages.orderCreated': '订单创建成功！',
                'messages.orderCreateFailed': '订单创建失败',
                'messages.formReset': '表单已重置',
                'messages.formResetConfirm': '确定要重置所有表单数据吗？',
                'messages.dataValidated': '数据验证通过',
                'messages.dataValidationFailed': '数据验证失败',
                'messages.priceValid': '价格有效',
                'messages.priceInvalid': '价格必须大于0',
                'messages.emailSaved': '默认邮箱已保存',
                'messages.emailCleared': '默认邮箱已清除',
                'messages.emailInvalid': '邮箱格式不正确',
                'messages.historyExported': '历史订单已导出',
                'messages.historyCleared': '历史订单已清空',
                'messages.historyClearConfirm': '确定要清空所有历史订单吗？此操作不可恢复。',
                'messages.aiParsingSuccess': 'AI自动解析成功',
                'messages.aiParsingFailed': 'AI解析失败',
                'messages.fallbackMode': '使用基础解析模式',
                'messages.pleaseInputOrder': '请输入订单描述',
                'messages.copied': '已复制',
                'messages.copyFailed': '复制失败',
                'messages.themeChanged': '主题已切换至{theme}模式',
                'messages.lightTheme': '亮色',
                'messages.darkTheme': '暗色',

                // 图片上传
                'image.uploadTitle': '📷 图片上传分析',
                'image.uploadText': '点击或拖拽图片到此处',
                'image.uploadHint': '支持 JPG, PNG, WebP 格式，最大 5MB',
                'image.analyzing': '正在分析图片内容...',
                'image.analyzed': '图片分析完成',
                'image.noText': '未检测到文本内容',
                'image.uploadFailed': '图片上传失败',
                'image.analysisFailed': '图片分析失败',
                'image.delete': '删除图片',

                // 价格转换
                'price.originalPrice': '原价',
                'price.convertedPrice': '转换后价格',
                'price.exchangeRate': '汇率',
                'price.conversionNote': '价格已转换',
                'price.currencySettings': '汇率设置',
                'price.updateRate': '更新汇率',
                'price.resetRates': '重置汇率',

                // 多订单
                'multiOrder.title': '🔢 多订单预览与编辑',
                'multiOrder.detected': '检测到多个订单',
                'multiOrder.orderCount': '{count} 个订单',
                'multiOrder.batchCreate': '批量创建',
                'multiOrder.selectAll': '全选',
                'multiOrder.deselectAll': '取消全选',
                'multiOrder.validateAll': '验证全部',
                'multiOrder.createSelected': '创建选中订单',
                'multiOrder.selectedCount': '已选择 {count} 个订单',
                'multiOrder.orderSequence': '订单 {sequence}',
                'multiOrder.edit': '编辑',
                'multiOrder.preview': '预览',
                'multiOrder.returnToMultiOrder': '🔢 返回多订单模式',

                // 举牌服务
                'paging.detected': '检测到举牌服务',
                'paging.orderGenerated': '已自动生成举牌订单',
                'paging.service': '举牌服务',
                'paging.meetAndGreet': '迎接服务',
                'paging.pagingPoint': '举牌服务点',

                // 验证错误消息
                'validation.customerNameRequired': '客户姓名为必填项',
                'validation.customerContactRequired': '联系电话为必填项',
                'validation.customerEmailRequired': '请填写邮箱地址',
                'validation.pickupRequired': '上车地点为必填项',
                'validation.dropoffRequired': '目的地为必填项',
                'validation.serviceDateRequired': '接送日期为必填项',
                'validation.serviceTimeRequired': '接送时间为必填项',
                'validation.serviceTypeRequired': '请选择服务类型',
                'validation.vehicleTypeRequired': '请选择车型',
                'validation.otaChannelRequired': '请选择OTA渠道',
                'validation.otaReferenceRequired': 'OTA参考号为必填项',
                'validation.drivingAreaRequired': '请选择行驶区域',
                'validation.passengerCountInvalid': '乘客人数必须大于0',
                'validation.luggageCountInvalid': '行李件数不能超过50件',
                'validation.emailFormatInvalid': '邮箱格式不正确',
                'validation.phoneFormatInvalid': '电话格式可能不正确',

                // 航班信息相关
                'flight.error.invalidFormat': '航班号格式无效',
                'flight.error.notFound': '航班信息未找到',
                'flight.error.networkError': '网络连接失败',
                'flight.error.apiError': 'API服务异常',
                'flight.error.timeout': '查询超时',
                'flight.error.authFailed': '认证失败',
                'flight.error.unknown': '查询失败',
                'flight.status.scheduled': '计划中',
                'flight.status.active': '飞行中',
                'flight.status.completed': '已完成',
                'flight.status.cancelled': '已取消',
                'flight.status.diverted': '改航',
                'flight.status.unknown': '未知'
            },
            
            en: {
                // Common
                'common.loading': 'Loading...',
                'common.success': 'Success',
                'common.error': 'Error',
                'common.warning': 'Warning',
                'common.info': 'Info',
                'common.confirm': 'Confirm',
                'common.cancel': 'Cancel',
                'common.close': 'Close',
                'common.save': 'Save',
                'common.delete': 'Delete',
                'common.edit': 'Edit',
                'common.search': 'Search',
                'common.reset': 'Reset',
                'common.export': 'Export',
                'common.clear': 'Clear',
                'common.copy': 'Copy',
                'common.view': 'View',
                'common.flightNumber': 'Flight Number: {flightNumber}',

                // Header Navigation
                'header.pageTitle': 'OTA Order Processing System - GoMyHire Integration',
                'header.title': 'OTA Order Processing System',
                'header.defaultEmail': 'Default Email:',
                'header.defaultEmailPlaceholder': 'Set default customer email',
                'header.defaultEmailTooltip': 'Set default customer email, auto-used when AI parsing cannot get email',
                'header.historyOrders': 'Order History',
                'header.logout': 'Logout',
                'header.toggleTheme': 'Toggle Theme',
                'header.language': 'Select Language',
                'header.languageZh': '中文',
                'header.languageEn': 'English',
                
                // Login Interface
                'login.title': 'User Login',
                'login.email': 'Email Address',
                'login.password': 'Password',
                'login.rememberMe': 'Remember Me',
                'login.loginButton': 'Login',
                'login.clearSaved': 'Clear Saved Account',
                'login.emailPlaceholder': 'Enter email address',
                'login.passwordPlaceholder': 'Enter password',
                
                // Smart Input Area
                'input.title': '📝 Order Input',
                'input.orderDescription': 'Order Description',
                'input.placeholder': 'Enter order description text, system will automatically parse order information...',
                'input.clearButton': 'Clear',
                'input.sampleButton': 'Sample Data',
                'input.parseButton': 'Manual Parse',
                'input.imageUpload': 'Image Upload',
                'input.geminiStatus': 'Please enter order description',
                
                // Order Preview
                'preview.title': '📋 Order Preview & Edit',
                'preview.validate': 'Validate',
                'preview.reset': 'Reset',
                'preview.close': 'Close',
                
                // Form Fields
                'form.basicInfo': 'Basic Information',
                'form.serviceType': 'Service Type',
                'form.serviceTypePlaceholder': 'Select service type',
                'form.serviceTypeTooltip': 'Select service type',
                'form.selectServiceType': 'Select service type',
                'form.subCategory': 'Sub Category',
                'form.subCategoryPlaceholder': 'Select sub category',
                'form.otaReference': 'OTA Reference',
                'form.otaReferencePlaceholder': 'OTA platform order number',
                'form.otaReferenceTooltip': 'OTA platform order reference number',
                'form.otaChannel': 'OTA Channel',
                'form.otaChannelPlaceholder': 'Select OTA channel',
                'form.otaChannelTooltip': 'Select OTA channel',
                'form.selectOtaChannel': 'Select OTA channel',
                'form.otaChannelCustom': 'Custom OTA',
                'form.carType': 'Car Type',
                'form.carTypePlaceholder': 'Select car type',
                'form.carTypeTooltip': 'Select car type',
                'form.selectCarType': 'Select car type',
                'form.incharge': 'In Charge',
                'form.inchargePlaceholder': 'Select person in charge',
                
                'form.customerInfo': 'Customer Information',
                'form.customerName': 'Customer Name',
                'form.customerNamePlaceholder': 'Customer name',
                'form.customerNameTooltip': 'Customer name',
                'form.customerPhone': 'Contact Phone',
                'form.customerContact': 'Contact Phone',
                'form.customerContactPlaceholder': 'Contact phone',
                'form.customerPhonePlaceholder': 'Contact phone',
                'form.customerPhoneTooltip': 'Customer contact phone',
                'form.customerEmail': 'Customer Email',
                'form.customerEmailPlaceholder': 'Customer email',
                'form.customerEmailTooltip': 'Customer email address',
                'form.flightInfo': 'Flight Info',
                'form.flightInfoPlaceholder': 'Flight number/Flight info',
                'form.flightInfoTooltip': 'Flight number or related flight information',
                
                'form.tripInfo': '🚗 Trip Information',
                'form.serviceConfig': '⚙️ Service Configuration',
                'form.pickup': 'Pickup Location',
                'form.pickupPlaceholder': 'Pickup location',
                'form.pickupTooltip': 'Customer pickup location',
                'form.dropoff': 'Destination',
                'form.dropoffPlaceholder': 'Destination',
                'form.dropoffTooltip': 'Customer destination',
                'form.destination': 'Destination',
                'form.destinationPlaceholder': 'Destination',
                'form.pickupDate': 'Pickup Date',
                'form.pickupDateTooltip': 'Pickup date',
                'form.pickupTime': 'Pickup Time',
                'form.pickupTimeTooltip': 'Pickup time',
                'form.date': 'Date',
                'form.time': 'Time',
                'form.passengerCount': 'Passenger Count',
                'form.passengerCountPlaceholder': 'Passenger count',
                'form.passengerCountTooltip': 'Number of passengers',
                'form.passengerNumber': 'Passengers',
                'form.luggageCount': 'Luggage Count',
                'form.luggageCountPlaceholder': 'Luggage count',
                'form.luggageCountTooltip': 'Number of luggage',
                'form.luggageNumber': 'Luggage',
                'form.specialRequests': 'Special Requests',
                'form.specialRequestsPlaceholder': 'Special requests or notes',
                
                'form.additionalInfo': 'Additional Information',
                'form.priceInfo': '💰 Price Information',
                'form.price': 'Price',
                'form.pricePlaceholder': 'Price',
                'form.priceTooltip': 'Order price',
                'form.currencyPlaceholder': 'Select currency',
                'form.currencyTooltip': 'Select currency',
                'form.otaPrice': 'OTA Price',
                'form.otaPricePlaceholder': 'OTA Price', 
                'form.otaPriceTooltip': 'OTA order price',
                'form.otaReferenceNumber': 'OTA Reference Number',
                'form.driverFee': 'Driver Fee',
                'form.driverFeePlaceholder': 'Driver Fee',
                'form.driverFeeTooltip': 'Driver service fee',
                'form.drivingRegion': 'Driving Region',
                'form.drivingRegionPlaceholder': 'Select driving region',
                'form.drivingRegionTooltip': 'Select driving region',
                'form.selectDrivingRegion': 'Select driving region',
                'form.languages': 'Required Languages',
                'form.languagesPlaceholder': 'Select language requirements',
                'form.languagesTooltip': 'Select language requirements',
                'form.selectLanguages': 'Select languages',
                
                // Service Types
                'serviceType.pickup': 'Pickup Service',
                'serviceType.dropoff': 'Dropoff Service',
                'serviceType.charter': 'Charter Service',
                'serviceType.paging': 'Paging Service',
                
                // Car Types
                'carType.5seater': '5 Seater',
                'carType.7seaterMpv': '7 Seater MPV',
                'carType.10seaterMpv': '10 Seater MPV',
                'carType.velfire': 'Velfire/Alphard',
                'carType.premium5': 'Premium 5 Seater',
                'carType.ticket': 'Ticket',
                'carType.7seaterSuv': '7 Seater SUV',
                'carType.extended5': 'Extended 5',
                'carType.4seaterHatch': '4 Seater Hatchback',
                'carType.ticketNonMalaysian': 'Ticket (Non-Malaysian)',
                'carType.14seaterVan': '14 Seater Van',
                'carType.18seaterVan': '18 Seater Van',
                'carType.30seatBus': '30 Seat Mini Bus',
                'carType.44seaterBus': '44 Seater Bus',
                
                // Regions
                'region.klSelangor': 'Kl/selangor',
                'region.penang': 'Penang',
                'region.johor': 'Johor',
                'region.sabah': 'Sabah',
                'region.singapore': 'Singapore',
                'region.ctrip': 'Ctrip Car',
                'region.complete': 'Complete',
                'region.paging': 'Paging',
                'region.charter': 'Charter',
                'region.malacca': 'Malacca',
                'region.sarawak': 'Sarawak',
                
                // History field labels
                'history.customer': 'Customer',
                'history.contact': 'Contact Phone',
                'history.email': 'Customer Email',
                'history.pickup': 'Pickup Location',
                'history.destination': 'Destination',
                'history.date': 'Date',
                'history.time': 'Time',
                'history.flight': 'Flight',
                'history.service': 'Service',
                'history.carType': 'Car Type',
                'history.region': 'Driving Region',
                'history.passengers': 'Passengers',
                'history.luggage': 'Luggage',
                'history.otaReference': 'OTA Reference',
                'history.requirements': 'Extra Requirements',
                'history.currency': 'Currency',
                
                // Copy function related  
                'form.orderId': 'Order ID',
                'form.orderType': 'Order Type',
                'copy.basicInfo': '=== Order Basic Info ===',
                'copy.tripInfo': '=== Trip Information ===',
                'copy.customerInfo': '=== Customer Information ===',
                'copy.serviceInfo': '=== Service Details ===',
                'copy.extraRequirements': '=== Special Requirements ===',
                'copy.notProvided': '(Not provided)',

                // Special Requirements
                'form.specialRequirements': 'Special Requirements',
                'form.babyChair': 'Baby Chair',
                'form.tourGuide': 'Tour Guide',
                'form.meetAndGreet': 'Meet & Greet',
                'form.extraRequirement': 'Extra Requirements',
                'form.extraRequirementTooltip': 'Other special requirements or notes',
                'form.extraRequirementPlaceholder': 'Other special requirements or notes',
                
                // Action Buttons
                'actions.previewOrder': 'Preview Order',
                'actions.createOrder': 'Create Order',
                'actions.resetForm': 'Reset Form',
                'actions.validateData': 'Validate Data',
                
                // Status Information
                'status.connected': 'Connected',
                'status.disconnected': 'Disconnected',
                'status.ready': 'Data Ready',
                'status.notReady': 'Data Not Ready',
                'status.waiting': 'Waiting for Data',
                
                // Order History
                'history.title': '📋 Order History Management',
                'history.export': 'Export',
                'history.clear': 'Clear',
                'history.searchOrderId': 'Order ID',
                'history.searchOrderIdPlaceholder': 'Search Order ID',
                'history.searchCustomer': 'Customer Name',
                'history.searchCustomerPlaceholder': 'Search Customer Name',
                'history.searchDateFrom': 'From Date',
                'history.searchDateTo': 'To Date',
                'history.searchButton': 'Search',
                'history.resetSearch': 'Reset',
                'history.statTotal': 'Total',
                'history.statToday': 'Today',
                'history.statWeek': 'This Week',
                'history.statMonth': 'This Month',
                'history.orderList': 'Order List',
                'history.recordCount': '{count} records total',
                'history.emptyState': 'No order history',
                'history.viewDetail': 'View Details',
                'history.copyOrder': 'Copy Order',
                'history.deleteOrder': 'Delete',
                
                // Messages
                'messages.loginSuccess': 'Login successful',
                'messages.loginFailed': 'Login failed',
                'messages.logoutSuccess': 'Successfully logged out',
                'messages.logoutConfirm': 'Are you sure you want to logout? Unsaved data may be lost.',
                'messages.orderCreated': 'Order created successfully!',
                'messages.orderCreateFailed': 'Failed to create order',
                'messages.formReset': 'Form has been reset',
                'messages.formResetConfirm': 'Are you sure you want to reset all form data?',
                'messages.dataValidated': 'Data validation passed',
                'messages.dataValidationFailed': 'Data validation failed',
                'messages.priceValid': 'Price is valid',
                'messages.priceInvalid': 'Price must be greater than 0',
                'messages.emailSaved': 'Default email saved',
                'messages.emailCleared': 'Default email cleared',
                'messages.emailInvalid': 'Invalid email format',
                'messages.historyExported': 'Order history exported',
                'messages.historyCleared': 'Order history cleared',
                'messages.historyClearConfirm': 'Are you sure you want to clear all order history? This action cannot be undone.',
                'messages.aiParsingSuccess': 'AI parsing successful',
                'messages.aiParsingFailed': 'AI parsing failed',
                'messages.fallbackMode': 'Using fallback parsing mode',
                'messages.pleaseInputOrder': 'Please enter order description',
                'messages.copied': 'Copied',
                'messages.copyFailed': 'Copy failed',
                'messages.themeChanged': 'Theme switched to {theme} mode',
                'messages.lightTheme': 'light',
                'messages.darkTheme': 'dark',

                // Image Upload
                'image.uploadTitle': '📷 Image Upload Analysis',
                'image.uploadText': 'Click or drag images here',
                'image.uploadHint': 'Supports JPG, PNG, WebP formats, max 5MB',
                'image.analyzing': 'Analyzing image content...',
                'image.analyzed': 'Image analysis completed',
                'image.noText': 'No text detected',
                'image.uploadFailed': 'Image upload failed',
                'image.analysisFailed': 'Image analysis failed',
                'image.delete': 'Delete image',

                // Price Conversion
                'price.originalPrice': 'Original Price',
                'price.convertedPrice': 'Converted Price',
                'price.exchangeRate': 'Exchange Rate',
                'price.conversionNote': 'Price converted',
                'price.currencySettings': 'Exchange Rate Settings',
                'price.updateRate': 'Update Rate',
                'price.resetRates': 'Reset Rates',

                // Multi Order
                'multiOrder.title': '🔢 Multi-Order Preview & Edit',
                'multiOrder.detected': 'Multiple orders detected',
                'multiOrder.orderCount': '{count} orders',
                'multiOrder.batchCreate': 'Batch Create',
                'multiOrder.selectAll': 'Select All',
                'multiOrder.deselectAll': 'Deselect All',
                'multiOrder.validateAll': 'Validate All',
                'multiOrder.createSelected': 'Create Selected Orders',
                'multiOrder.selectedCount': '{count} orders selected',
                'multiOrder.orderSequence': 'Order {sequence}',
                'multiOrder.edit': 'Edit',
                'multiOrder.preview': 'Preview',
                'multiOrder.returnToMultiOrder': '🔢 Return to Multi-Order Mode',

                // Paging Service
                'paging.detected': 'Paging service detected',
                'paging.orderGenerated': 'Paging order auto-generated',
                'paging.service': 'Paging Service',
                'paging.meetAndGreet': 'Meet & Greet',
                'paging.pagingPoint': 'Paging Service Point',

                // Validation Messages
                'validation.customerNameRequired': 'Customer name is required',
                'validation.customerContactRequired': 'Contact phone is required',
                'validation.customerEmailRequired': 'Please fill in email address',
                'validation.pickupRequired': 'Pickup location is required',
                'validation.dropoffRequired': 'Destination is required',
                'validation.serviceDateRequired': 'Service date is required',
                'validation.serviceTimeRequired': 'Service time is required',
                'validation.serviceTypeRequired': 'Please select service type',
                'validation.vehicleTypeRequired': 'Please select vehicle type',
                'validation.otaChannelRequired': 'Please select OTA channel',
                'validation.otaReferenceRequired': 'OTA reference number is required',
                'validation.drivingAreaRequired': 'Please select driving area',
                'validation.passengerCountInvalid': 'Passenger count must be greater than 0',
                'validation.luggageCountInvalid': 'Luggage count cannot exceed 50 pieces',
                'validation.emailFormatInvalid': 'Email format is incorrect',
                'validation.phoneFormatInvalid': 'Phone format may be incorrect',

                // Flight Information
                'flight.error.invalidFormat': 'Invalid flight number format',
                'flight.error.notFound': 'Flight information not found',
                'flight.error.networkError': 'Network connection failed',
                'flight.error.apiError': 'API service error',
                'flight.error.timeout': 'Query timeout',
                'flight.error.authFailed': 'Authentication failed',
                'flight.error.unknown': 'Query failed',
                'flight.status.scheduled': 'Scheduled',
                'flight.status.active': 'Active',
                'flight.status.completed': 'Completed',
                'flight.status.cancelled': 'Cancelled',
                'flight.status.diverted': 'Diverted',
                'flight.status.unknown': 'Unknown'
            }
        };
    }

    /**
     * 获取翻译文本
     * @param {string} key - 翻译键
     * @param {Object} params - 参数对象
     * @returns {string} 翻译后的文本
     */
    t(key, params = {}) {
        const translation = this.translations[this.currentLanguage]?.[key] || 
                          this.translations['zh']?.[key] || 
                          key;
        
        // 替换参数
        return translation.replace(/\{(\w+)\}/g, (match, paramKey) => {
            return params[paramKey] !== undefined ? params[paramKey] : match;
        });
    }

    /**
     * 切换语言
     * @param {string} language - 语言代码
     */
    setLanguage(language) {
        if (this.translations[language]) {
            this.currentLanguage = language;

            // 保存到localStorage
            localStorage.setItem(this.storageKey, language);

            // 保存到AppState（如果可用）
            if (window.OTA && window.OTA.appState) {
                window.OTA.appState.set('config.language', language);
            }

            // 更新界面
            this.updateUI();

            // 更新已显示的错误消息
            this.updateFieldErrors();

            getLogger().log(`语言已切换为: ${language}`, 'info');
        }
    }

    /**
     * 获取当前语言
     * @returns {string} 当前语言代码
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * 更新界面文本
     */
    updateUI() {
        // 更新语言选择下拉菜单的选中状态
        const languageSelect = document.getElementById('languageSelect');
        if (languageSelect) {
            languageSelect.value = this.currentLanguage;
        }

        // 保存表单数据（防止语言切换时丢失）
        const preservedFormData = this.preserveFormData();

        // **修复**: 确保OTA下拉菜单已填充完成
        this.ensureOtaOptionsArePopulated();

        // 更新所有带有 data-i18n 属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);

            if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'email' || element.type === 'password' || element.type === 'tel' || element.type === 'number')) {
                element.placeholder = translation;
            } else if (element.tagName === 'TEXTAREA') {
                element.placeholder = translation;
            } else if (element.tagName === 'INPUT' && element.type === 'submit') {
                element.value = translation;
            } else if (element.tagName === 'TITLE') {
                document.title = translation;
            } else if (element.tagName === 'SELECT') {
                // **修复**: 对SELECT元素进行特殊处理，避免清除option子元素
                if (element.hasAttribute('data-preserve-value')) {
                    // 对于有特殊标记的select元素，只更新选项文本，不改变选中值
                    this.updateSelectOptionsText(element, key);
                } else {
                    // 对于普通select元素，不设置textContent以避免清除option元素
                    // 如果需要更新placeholder选项，通过专门的方法处理
                    this.updateSelectPlaceholder(element, key);
                }
            } else {
                element.textContent = translation;
            }
        });
        
        // 更新所有带有 data-i18n-title 属性的元素的title
        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });

        // 恢复表单数据
        this.restoreFormData(preservedFormData);
        
        // 触发自定义事件
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage }
        }));

        // 语言切换后，重新初始化下拉菜单选项（如果需要）
        setTimeout(() => {
            if (window.OTA && window.OTA.uiManager && window.OTA.uiManager.managers && window.OTA.uiManager.managers.form) {
                // 重新填充所有下拉菜单选项
                window.OTA.uiManager.managers.form.populateOtaChannelOptions();
                
                // 重新填充其他下拉菜单
                const formManager = window.OTA.uiManager.managers.form;
                if (formManager.elements) {
                    // 更新服务类型下拉菜单的placeholder
                    if (formManager.elements.sub_category_id) {
                        const firstOption = formManager.elements.sub_category_id.querySelector('option[value=""]');
                        if (firstOption) {
                            firstOption.textContent = this.t('form.selectServiceType');
                        }
                    }

                    // OTA渠道下拉菜单已不再使用占位符，跳过占位符文本更新

                    // 更新车型下拉菜单的placeholder
                    if (formManager.elements.car_type_id) {
                        const firstOption = formManager.elements.car_type_id.querySelector('option[value=""]');
                        if (firstOption) {
                            firstOption.textContent = this.t('form.selectCarType');
                        }
                    }

                    // 更新行驶区域下拉菜单的placeholder
                    if (formManager.elements.driving_region_id) {
                        const firstOption = formManager.elements.driving_region_id.querySelector('option[value=""]');
                        if (firstOption) {
                            firstOption.textContent = this.t('form.selectDrivingRegion');
                        }
                    }
                    
                    // 更新语言选择下拉菜单的placeholder
                    if (formManager.elements.languagesIdArray) {
                        const trigger = document.querySelector('#languagesTrigger .multi-select-text');
                        if (trigger && (trigger.textContent.includes('请选择语言') || trigger.textContent.includes('Select languages'))) {
                            trigger.textContent = this.t('form.selectLanguages');
                        }
                    }
                }
                
                getLogger().log('语言切换后重新填充下拉菜单选项', 'info');
            }
        }, 100);
    }

    /**
     * 保存表单数据
     */
    preserveFormData() {
        const formData = {};
        
        // 保存重要的表单字段值
        const importantFields = ['ota', 'otaChannelCustom', 'defaultEmail'];
        importantFields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                formData[fieldId] = element.value;
            }
        });
        
        return formData;
    }

    /**
     * 恢复表单数据
     */
    restoreFormData(formData) {
        Object.keys(formData).forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element && formData[fieldId] !== undefined) {
                element.value = formData[fieldId];
            }
        });
    }

    /**
     * 更新select选项的文本但保持选中值
     */
    updateSelectOptionsText(selectElement, i18nKey) {
        // 保存当前选中的值
        const currentValue = selectElement.value;

        // 这里可以根据需要更新选项文本
        // 暂时跳过，因为OTA渠道选项由专门的函数管理

        // 恢复选中值
        if (currentValue) {
            selectElement.value = currentValue;
        }
    }

    /**
     * 更新select元素的placeholder选项，不影响其他选项
     * @param {HTMLSelectElement} selectElement - select元素
     * @param {string} i18nKey - 国际化键名
     */
    updateSelectPlaceholder(selectElement, i18nKey) {
        // 查找第一个空值选项（通常是placeholder）
        const placeholderOption = selectElement.querySelector('option[value=""]');
        if (placeholderOption) {
            // 只更新placeholder选项的文本，不影响其他选项
            const translation = this.t(i18nKey);
            placeholderOption.textContent = translation;
        }
        // 如果没有找到placeholder选项，则不做任何处理，保持select元素完整性
    }

    /**
     * 确保OTA下拉菜单已填充完成
     * @description 检查OTA下拉菜单是否已填充，如果未填充则触发填充
     */
    ensureOtaOptionsArePopulated() {
        const otaSelect = document.getElementById('ota');
        if (otaSelect && otaSelect.children.length <= 1) {
            // 如果OTA下拉菜单只有一个占位符选项，尝试重新填充
            if (window.OTA && window.OTA.uiManager && window.OTA.uiManager.managers && window.OTA.uiManager.managers.form) {
                window.OTA.uiManager.managers.form.populateOtaChannelOptions();
                getLogger().log('OTA下拉菜单已重新填充', 'info');
            }
        }
    }

    /**
     * 获取支持的语言列表
     * @returns {Array} 语言列表
     */
    getSupportedLanguages() {
        return Object.keys(this.translations);
    }

    /**
     * 更新已显示的字段错误消息
     * @description 当语言切换时，重新翻译并更新页面上已显示的错误消息
     */
    updateFieldErrors() {
        // 查找所有显示的错误消息元素
        const errorElements = document.querySelectorAll('.field-error');

        if (errorElements.length === 0) {
            return; // 没有错误消息需要更新
        }

        // 获取EventManager和FormManager来重新验证
        try {
            const eventManager = window.OTA.getService('eventManager');
            const formManager = window.OTA.getService('formManager');

            if (!eventManager || !formManager) {
                getLogger().log('无法获取EventManager或FormManager，跳过错误消息更新', 'warn');
                return;
            }

            // 收集当前表单数据
            const formData = formManager.collectFormData();

            // 重新检测字段问题
            const fieldIssues = eventManager.detectFieldIssues(formData);

            // 清除所有现有错误消息
            eventManager.clearAllFieldErrors(formManager);

            // 重新显示错误消息（使用新语言）
            Object.keys(fieldIssues).forEach(fieldName => {
                formManager.showFieldError(fieldName, fieldIssues[fieldName]);
            });

            getLogger().log(`已更新 ${Object.keys(fieldIssues).length} 个字段错误消息的语言`, 'info');

        } catch (error) {
            getLogger().log(`更新字段错误消息时出错: ${error.message}`, 'error');
        }
    }
}

// 创建全局实例 - 立即创建以确保在依赖注册时可用
let i18nManagerInstance = new I18nManager();

/**
 * 获取国际化管理器实例
 * @returns {I18nManager} 管理器实例
 */
function getI18nManager() {
    return i18nManagerInstance;
}

/**
 * 翻译文本的快捷函数
 * @param {string} key - 翻译键
 * @param {Object} params - 参数对象
 * @returns {string} 翻译后的文本
 */
function t(key, params = {}) {
    return getI18nManager().t(key, params);
}

// 导出到全局作用域
window.I18nManager = I18nManager;
window.getI18nManager = getI18nManager;
window.t = t;

// 确保OTA命名空间存在并暴露管理器
window.OTA = window.OTA || {};
window.OTA.I18nManager = I18nManager;
window.OTA.getI18nManager = getI18nManager;
window.OTA.i18nManager = i18nManagerInstance; // 直接使用已创建的实例
window.OTA.t = t;

// 向后兼容
window.i18nManager = i18nManagerInstance; // 直接使用已创建的实例
