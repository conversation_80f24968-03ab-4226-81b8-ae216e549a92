index.html:441 🔍 临时启用详细日志记录...
script-manifest.js:171 [ScriptManifest v2.0] 🚀 优化加载架构已就绪
script-loader.js:171 ✅ ScriptLoader ready
script-loader.js:128 🔧 Loading phase: infrastructure (8 scripts)
dependency-container.js?v=2.4.2:298 ✅ 依赖容器已初始化
service-locator.js?v=2.4.2:90 ✅ 服务定位器已初始化
service-locator.js?v=2.4.2:424 ✅ 服务定位器已加载
script-loader.js?v=2.4.2:171 ✅ ScriptLoader ready
script-loader.js?v=2.4.2:128 🔧 Loading phase: infrastructure (8 scripts)
dependency-container.js?v=2.4.2:233 [DependencyContainer] 已注册服务: lifecycleManager
application-bootstrap.js?v=2.4.2:522 ✅ 应用启动协调器已加载
logger.js?v=2.4.2:222 ✅ Phase complete: infrastructure in 49.7ms
logger.js?v=2.4.2:222 🔧 Loading phase: configuration (7 scripts)
logger.js?v=2.4.2:222 [VersionWatcher] 检测到本地 file 协议，禁用远程版本轮询 (需要通过本地静态服务器运行以启用热版本检测)
logger.js?v=2.4.2:222 ✅ Phase complete: infrastructure in 130.2ms
logger.js?v=2.4.2:222 🔧 Loading phase: configuration (7 scripts)
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: eventCoordinator
logger.js?v=2.4.2:222 ✅ 特性开关机制已加载
logger.js?v=2.4.2:222 ✅ FormManager类定义完成（阶段2: 配置和类定义）
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: vehicleConfigManager
logger.js?v=2.4.2:222 ✅ 车辆配置管理器已加载
logger.js?v=2.4.2:222 ✅ Phase complete: configuration in 90.9ms
logger.js?v=2.4.2:222 🔧 Loading phase: services (39 scripts)
logger.js?v=2.4.2:222 ✅ 精简酒店数据已加载 {version: '1.0.0', totalHotels: 57, source: 'essential_inline_data', optimizedFor: 'startup_performance', sizeReduction: '90%', …}
logger.js?v=2.4.2:222 ✅ Phase complete: configuration in 103.7ms
logger.js?v=2.4.2:222 🔧 Loading phase: services (39 scripts)
logger.js?v=2.4.2:222 ✅ 统一OTA策略配置已加载
logger.js?v=2.4.2:222 🌐 开始初始化统一语言检测器...
logger.js?v=2.4.2:222 ✅ 统一语言检测器初始化成功
logger.js?v=2.4.2:222 ✅ ChannelDetector (子层实现) 已加载
logger.js?v=2.4.2:222 ✅ PromptBuilder (子层实现) 已加载
logger.js?v=2.4.2:222 ✅ GeminiCaller (子层实现) 已加载
logger.js?v=2.4.2:222 ✅ ResultProcessor (子层实现) 已加载
logger.js?v=2.4.2:222 ✅ OrderParser (子层实现) 已加载
logger.js?v=2.4.2:222 ✅ KnowledgeBase (简化版 - 本地数据) 已加载 {version: '3.0.0', architecture: 'simplified_local_data', features: Array(3)}
logger.js?v=2.4.2:222 ✅ 酒店数据库已加载 {totalHotels: 4264, metadata: {…}}
logger.js?v=2.4.2:222 ✅ 智能地址翻译器已加载 {version: '4.0.0', strategy: '智能本地映射优先 + Gemini AI', features: Array(4), mappings: {…}}
logger.js?v=2.4.2:222 ✅ MultiOrderHandler (子层实现) 已加载
logger.js?v=2.4.2:222 ✅ APICaller (子层实现) 已加载
logger.js?v=2.4.2:222 ✅ HistoryManager (子层实现) 已加载
logger.js?v=2.4.2:222 ✅ BusinessFlowController (母层控制器) 已加载
logger.js?v=2.4.2:222 ✅ OrderManagementController (母层控制器) 已加载
logger.js?v=2.4.2:222 ✅ GeminiServiceAdapter (兼容性适配器) 已加载
logger.js?v=2.4.2:222 ✅ UIManagerAdapter 已加载并注册
logger.js?v=2.4.2:487 [6:26:48 PM] [ERROR] ScriptLoader failed: Error: Failed to load script: js/adapters/multi-order-manager-adapter.js?v=2.4.2 
outputToConsole @ logger.js?v=2.4.2:487
log @ logger.js?v=2.4.2:407
console.error @ logger.js?v=2.4.2:236
(anonymous) @ script-loader.js?v=2.4.2:160
Promise.catch
maybeAutoStart @ script-loader.js?v=2.4.2:160
setTimeout
(anonymous) @ script-loader.js?v=2.4.2:166
(anonymous) @ script-loader.js?v=2.4.2:172
logger.js?v=2.4.2:237 ScriptLoader failed: Error: Failed to load script: js/adapters/multi-order-manager-adapter.js?v=2.4.2
    at script.onerror (script-loader.js?v=2.4.2:92:37)
console.error @ logger.js?v=2.4.2:237
(anonymous) @ script-loader.js?v=2.4.2:160
Promise.catch
maybeAutoStart @ script-loader.js?v=2.4.2:160
setTimeout
(anonymous) @ script-loader.js?v=2.4.2:166
(anonymous) @ script-loader.js?v=2.4.2:172
logger.js?v=2.4.2:222 ✅ 统一字段映射服务已加载 {version: '1.0.0', strategy: '统一数据契约 + 简化架构', features: Array(5), defaultValues: 10}
logger.js?v=2.4.2:222 🔍 unified-field-mapper 实例验证成功 {hasProcessData: true, hasConvertDataTypes: true, hasApplyDefaultValues: true}
logger.js?v=2.4.2:222 ✅ 内联酒店数据模块已加载
logger.js?v=2.4.2:222 ✅ 简单路由系统已加载
logger.js?v=2.4.2:222 ✅ 页面管理器已加载
logger.js?v=2.4.2:222 ✅ 基础组件类已加载
logger.js?v=2.4.2:222 ✅ 组件注册中心已加载
logger.js?v=2.4.2:222 ✅ 订单卡片组件已加载
logger.js?v=2.4.2:222 ✅ 批量操作控件组件已加载
logger.js?v=2.4.2:222 ✅ 进度指示器组件已加载
logger.js?v=2.4.2:222 ✅ 状态面板组件已加载
logger.js?v=2.4.2:222 ✅ 订单检测服务已加载
logger.js?v=2.4.2:222 ✅ 订单处理服务已加载
logger.js?v=2.4.2:222 ✅ 批量管理服务已加载
logger.js?v=2.4.2:222 ✅ 状态管理服务已加载
logger.js?v=2.4.2:222 ✅ API客户端服务已加载
logger.js?v=2.4.2:222 ✅ 多订单页面控制器V2已加载
logger.js?v=2.4.2:222 ✅ Phase complete: services in 716.3ms
logger.js?v=2.4.2:222 🔧 Loading phase: managers (11 scripts)
logger.js?v=2.4.2:222 🔧 无感知更新配置已加载
logger.js?v=2.4.2:222 ✅ 权限管理器已加载
logger.js?v=2.4.2:222 ✅ 动画管理器已加载并初始化
logger.js?v=2.4.2:222 ✅ BaseManager适配器已加载，BaseManager全局类已可用
logger.js?v=2.4.2:222 ✅ 统一版OTA管理器已加载
logger.js?v=2.4.2:222 ✅ Phase complete: managers in 118.5ms
logger.js?v=2.4.2:222 🔧 Loading phase: launch (2 scripts)
logger.js?v=2.4.2:222 🚀 开始启动OTA订单处理系统...
logger.js?v=2.4.2:222 🚀 开始启动OTA订单处理系统...
logger.js?v=2.4.2:222 📋 执行启动阶段: dependencies (1/5)
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: appState
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: logger
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: utils
logger.js?v=2.4.2:222 [DependencyContainer] 警告: 服务 eventCoordinator 已存在，将被覆盖
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: eventCoordinator
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: apiService
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: geminiService
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: i18nManager
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: imageUploadManager
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: currencyConverter
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: multiOrderManager
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: orderHistoryManager
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: pagingServiceManager
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: uiManager
logger.js?v=2.4.2:222 [DependencyContainer] 已注册服务: channelDetector
logger.js?v=2.4.2:222 📦 已注册 14 个依赖
logger.js?v=2.4.2:222 📋 执行启动阶段: services (2/5)
logger.js?v=2.4.2:222 [DependencyContainer] 已创建服务实例: appState
logger.js?v=2.4.2:222 ⚙️ 已初始化 6 个核心服务
logger.js?v=2.4.2:222 📋 执行启动阶段: managers (3/5)
logger.js?v=2.4.2:222 🎛️ 已处理 5 个管理器
logger.js?v=2.4.2:222 📋 执行启动阶段: ui (4/5)
logger.js?v=2.4.2:222 ✅ Phase complete: launch in 45.5ms
logger.js?v=2.4.2:222 🚀 All scripts loaded in 1102.5ms
logger.js?v=2.4.2:222 [UIManager] ✅ FormManager类和实例已统一注册（浏览器兼容性增强）
logger.js?v=2.4.2:222 [UIManager.showWorkspace] body 添加 logged-in (唯一控制源), class= logged-in
logger.js?v=2.4.2:222 [Observer] body.class 变更 => logged-in
logger.js?v=2.4.2:222 [Observer] body.class 变更 => logged-in
logger.js?v=2.4.2:222 🎨 用户界面初始化完成
logger.js?v=2.4.2:222 📋 执行启动阶段: finalization (5/5)
logger.js?v=2.4.2:222 🏁 系统启动完成
logger.js?v=2.4.2:222 ✅ OTA系统启动完成，总耗时: 47.60ms
application-bootstrap.js?v=2.4.2:486 📊 启动报告
logger.js?v=2.4.2:222 ✅ dependencies: 4.20ms
logger.js?v=2.4.2:222    详情: 已注册: appState, 已注册: logger, 已注册: utils, 已注册: eventCoordinator, 已注册: apiService, 已注册: geminiService, 已注册: i18nManager, 已注册: imageUploadManager, 已注册: currencyConverter, 已注册: multiOrderManager, 已注册: orderHistoryManager, 已注册: pagingServiceManager, 已注册: uiManager, 已注册: channelDetector
logger.js?v=2.4.2:222 ✅ services: 2.00ms
logger.js?v=2.4.2:222    详情: 已初始化: appState, 已初始化: logger, 已初始化: utils, 已初始化: eventCoordinator, 已初始化: apiService, 已初始化: geminiService
logger.js?v=2.4.2:222 ✅ managers: 1.10ms
logger.js?v=2.4.2:222    详情: 已初始化: imageUploadManager, 已初始化: currencyConverter, 已初始化: multiOrderManager, 已初始化: orderHistoryManager, 已初始化: pagingServiceManager
logger.js?v=2.4.2:222 ✅ ui: 37.60ms
logger.js?v=2.4.2:222    详情: 国际化管理器已初始化, UI管理器已初始化
logger.js?v=2.4.2:222 ✅ finalization: 1.10ms
logger.js?v=2.4.2:222    详情: 健康检查: 90/100, 全局错误处理已设置, 调试接口已暴露, 字段标准化层已禁用（架构简化）
logger.js?v=2.4.2:222 ✅ OTA系统启动成功，耗时: 47.60ms
logger.js?v=2.4.2:222 🚀 初始化页面系统...
logger.js?v=2.4.2:487 [6:26:49 PM] [ERROR] 多订单页面元素未找到 {type: 'error', error: '多订单页面元素未找到', context: {…}}
outputToConsole @ logger.js?v=2.4.2:487
log @ logger.js?v=2.4.2:407
logError @ logger.js?v=2.4.2:611
cachePageElements @ page-manager.js?v=2.4.2:95
init @ page-manager.js?v=2.4.2:72
initializePageSystem @ main.js?v=2.4.2:77
startApp @ main.js?v=2.4.2:152
await in startApp
(anonymous) @ main.js?v=2.4.2:194
logger.js?v=2.4.2:222 🔗 多订单检测触发逻辑已设置
logger.js?v=2.4.2:222 ✅ 页面系统初始化完成
logger.js?v=2.4.2:222 🔍 getDefaultBackendUserId 开始执行 {currentUser: {…}, timestamp: '2025-09-07T10:26:49.477Z', localStorageEmail: null, appStateAuth: {…}, completeUserList: '45 users available'}
logger.js?v=2.4.2:222 🔍 getDefaultBackendUserId 开始执行 {currentUser: {…}, timestamp: '2025-09-07T10:26:49.827Z', localStorageEmail: null, appStateAuth: {…}, completeUserList: '45 users available'}
logger.js?v=2.4.2:496 [6:26:50 PM] [INFO] 🔍 详细日志已启用（调试模式） 
logger.js?v=2.4.2:496 [6:26:50 PM] [INFO] ✅ 详细日志记录已启用 
logger.js?v=2.4.2:222 ✅ 详细日志记录已启用
logger.js?v=2.4.2:496 [6:26:52 PM] [INFO] 实时分析已禁用 
logger.js?v=2.4.2:496 [6:27:01 PM] [INFO] 实时分析已启用 
logger.js?v=2.4.2:496 [6:27:02 PM] [INFO] 🌐 检测到中文内容，已自动设置中文语言要求 {sourceField: 'unknown', textLength: 103, hasChinese: true, languageIds: Array(1)}
logger.js?v=2.4.2:496 [6:27:02 PM] [INFO] ⚠️ 检测到重复输入调用，跳过处理 {timeDiff: 88, isPasteEvent: true}
logger.js?v=2.4.2:496 [6:27:02 PM] [INFO] 🌐 检测到中文内容，已自动设置中文语言要求 {sourceField: '#orderInput', textLength: 103, hasChinese: true, languageIds: Array(1)}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 🌐 检测到中文内容，已自动设置中文语言要求 {sourceField: '#orderInput', textLength: 103, hasChinese: true, languageIds: Array(1)}
realtime-analysis-manager.js?v=2.4.2:415 🔍 多订单数据流追踪 - 第1步：实时分析触发
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 输入文本长度: 103 
logger.js?v=2.4.2:222 输入文本长度: 103
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 输入文本预览: Joshua: 接机：

团号：EJBTBY250712-1
2PAX
13/7 KLIA IN 0100 (MF857) - MOXY PUTRAJAYA
客人：朱芸 
客人联系：18130403306
... 
logger.js?v=2.4.2:222 输入文本预览: Joshua: 接机：

团号：EJBTBY250712-1
2PAX
13/7 KLIA IN 0100 (MF857) - MOXY PUTRAJAYA
客人：朱芸 
客人联系：18130403306
...
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 每次输入都进行新的渠道检测 {inputLength: 103, cacheDisabled: true}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 渠道检测器已初始化 
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 开始渠道检测 {inputLength: 103}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 未检测到特定渠道 
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 实时渠道检测完成 {channel: null, confidence: 0, method: 'no_match'}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 🔍 获取到的渠道检测结果: [object Object] 
logger.js?v=2.4.2:222 🔍 获取到的渠道检测结果: {channel: null, confidence: 0, method: 'no_match'}
realtime-analysis-manager.js?v=2.4.2:449 🔍 多订单数据流追踪 - 第2步：调用Gemini解析
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 🔄 开始实时订单解析... 
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 适配器：解析订单文本 {textLength: 103, isRealtime: true, hasChannelInfo: true}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 开始处理输入 {type: 'text', inputLength: 103, autoTriggered: false, sourceField: 'unknown'}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 预检测结果无效，进行新的渠道检测 {invalidResult: {…}}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 开始渠道检测 {inputLength: 103}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 未检测到特定渠道 
logger.js?v=2.4.2:496 [6:27:03 PM] [DEBUG] 🔧 [调试] 开始构建提示词 {channel: null, confidence: 0, hasStrategy: false}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 开始构建提示词 {channel: null, inputLength: 103, autoTriggered: false, sourceField: 'unknown'}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 使用通用提示词 
logger.js?v=2.4.2:496 [6:27:03 PM] [DEBUG] 🔧 [调试] 提示词构建完成 {promptLength: 5843, containsChannelInfo: true}
logger.js?v=2.4.2:496 [6:27:03 PM] [INFO] 开始调用Gemini API {type: 'text', promptLength: 5843, isRealtime: true}
logger.js?v=2.4.2:493 [6:27:05 PM] [SUCCESS] Gemini API调用成功: 2456.80ms {timeout: 25000, model: 'gemini-2.5-flash-lite'}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"customer_name\": \"朱芸\",\n  \"customer_contact\": \"18130403306\",\n  \"customer_email\": null,\n  \"ota\": null,\n  \"ota_reference_number\": \"EJBTBY250712-1\",\n  \"flight_info\": \"MF857\",\n  \"departure_time\": null,\n  \"arrival_time\": \"01:00\",\n  \"flight_type\": \"Arrival\",\n  \"date\": \"2024-07-13\",\n  \"time\": \"01:00\",\n  \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n  \"destination\": \"Moxy Putrajaya\",\n  \"passenger_number\": 2,\n  \"luggage_number\": null,\n  \"sub_category_id\": 2,\n  \"car_type_id\": null,\n  \"driving_region_id\": 1,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": null,\n  \"currency\": null,\n  \"extra_requirement\": null\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 2804,
    "candidatesTokenCount": 294,
    "totalTokenCount": 3098,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 2804
      }
    ]
  },
  "modelVersion": "gemini-2.5-flash-lite",
  "responseId": "-l29aMOPEZeIqtsP8tmI4QI"
} 
logger.js?v=2.4.2:222 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"customer_name\": \"朱芸\",\n  \"customer_contact\": \"18130403306\",\n  \"customer_email\": null,\n  \"ota\": null,\n  \"ota_reference_number\": \"EJBTBY250712-1\",\n  \"flight_info\": \"MF857\",\n  \"departure_time\": null,\n  \"arrival_time\": \"01:00\",\n  \"flight_type\": \"Arrival\",\n  \"date\": \"2024-07-13\",\n  \"time\": \"01:00\",\n  \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n  \"destination\": \"Moxy Putrajaya\",\n  \"passenger_number\": 2,\n  \"luggage_number\": null,\n  \"sub_category_id\": 2,\n  \"car_type_id\": null,\n  \"driving_region_id\": 1,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": null,\n  \"currency\": null,\n  \"extra_requirement\": null\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 2804,
    "candidatesTokenCount": 294,
    "totalTokenCount": 3098,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 2804
      }
    ]
  },
  "modelVersion": "gemini-2.5-flash-lite",
  "responseId": "-l29aMOPEZeIqtsP8tmI4QI"
}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n  \"customer_name\": \"朱芸\",\n  \"customer_contact\": \"18130403306\",\n  \"customer_email\": null,\n  \"ota\": null,\n  \"ota_reference_number\": \"EJBTBY250712-1\",\n  \"flight_info\": \"MF857\",\n  \"departure_time\": null,\n  \"arrival_time\": \"01:00\",\n  \"flight_type\": \"Arrival\",\n  \"date\": \"2024-07-13\",\n  \"time\": \"01:00\",\n  \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n  \"destination\": \"Moxy Putrajaya\",\n  \"passenger_number\": 2,\n  \"luggage_number\": null,\n  \"sub_category_id\": 2,\n  \"car_type_id\": null,\n  \"driving_region_id\": 1,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": null,\n  \"currency\": null,\n  \"extra_requirement\": null\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
} 
logger.js?v=2.4.2:222 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n  \"customer_name\": \"朱芸\",\n  \"customer_contact\": \"18130403306\",\n  \"customer_email\": null,\n  \"ota\": null,\n  \"ota_reference_number\": \"EJBTBY250712-1\",\n  \"flight_info\": \"MF857\",\n  \"departure_time\": null,\n  \"arrival_time\": \"01:00\",\n  \"flight_type\": \"Arrival\",\n  \"date\": \"2024-07-13\",\n  \"time\": \"01:00\",\n  \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n  \"destination\": \"Moxy Putrajaya\",\n  \"passenger_number\": 2,\n  \"luggage_number\": null,\n  \"sub_category_id\": 2,\n  \"car_type_id\": null,\n  \"driving_region_id\": 1,\n  \"baby_chair\": null,\n  \"tour_guide\": null,\n  \"meet_and_greet\": null,\n  \"needs_paging_service\": null,\n  \"ota_price\": null,\n  \"currency\": null,\n  \"extra_requirement\": null\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
}
logger.js?v=2.4.2:493 [6:27:05 PM] [SUCCESS] Gemini API HTTP调用成功 {type: 'text', hasResult: true, resultType: 'object', hasStandardizedAddress: false}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 开始处理结果 {hasGeminiResult: true, channel: null}
logger.js?v=2.4.2:493 [6:27:05 PM] [SUCCESS] 结果处理完成 {type: 'single-order', orderCount: 1}
logger.js?v=2.4.2:493 [6:27:05 PM] [SUCCESS] 输入处理完成 {channel: null, resultType: 'single-order', autoTriggered: false}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] [GeminiServiceAdapter] 🔧 业务流控制器原始结果: [object Object] 
logger.js?v=2.4.2:222 [GeminiServiceAdapter] 🔧 业务流控制器原始结果: {type: 'single-order', order: {…}, orders: Array(1), channel: null, confidence: 0.8, …}
gemini-service-adapter.js?v=2.4.2:218 🤖 Gemini parseOrder 返回
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] meta: [object Object] 
logger.js?v=2.4.2:222 meta: {isRealtime: true}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] data (object): [object Object] 
logger.js?v=2.4.2:222 data (object): [{…}]
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] data (json):
[
  {
    "customer_name": "朱芸",
    "customer_contact": "18130403306",
    "customer_email": null,
    "ota_reference_number": "EJBTBY250712-1",
    "flight_info": "MF857",
    "date": "2024-07-13",
    "time": "01:00",
    "pickup": "Kuala Lumpur International Airport (KLIA1)",
    "destination": "Moxy Putrajaya",
    "car_type_id": null,
    "sub_category_id": 2,
    "driving_region_id": 1,
    "passenger_number": 2,
    "luggage_number": null,
    "languages_id_array": null,
    "extra_requirement": null,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "ota_price": null,
    "currency": null,
    "incharge_by_backend_user_id": null,
    "ota": null,
    "raw_text": null
  }
] 
logger.js?v=2.4.2:222 data (json):
[
  {
    "customer_name": "朱芸",
    "customer_contact": "18130403306",
    "customer_email": null,
    "ota_reference_number": "EJBTBY250712-1",
    "flight_info": "MF857",
    "date": "2024-07-13",
    "time": "01:00",
    "pickup": "Kuala Lumpur International Airport (KLIA1)",
    "destination": "Moxy Putrajaya",
    "car_type_id": null,
    "sub_category_id": 2,
    "driving_region_id": 1,
    "passenger_number": 2,
    "luggage_number": null,
    "languages_id_array": null,
    "extra_requirement": null,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "ota_price": null,
    "currency": null,
    "incharge_by_backend_user_id": null,
    "ota": null,
    "raw_text": null
  }
]
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] Gemini parseResult: [object Object] 
logger.js?v=2.4.2:222 Gemini parseResult: [{…}]
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 🔍 parseOrder返回结果调试 {parseResult: Array(1), isArray: true, length: 1, type: 'object'}
logger.js?v=2.4.2:493 [6:27:05 PM] [SUCCESS] ✅ 解析完成，检测到 1 个订单 
realtime-analysis-manager.js?v=2.4.2:507 🔍 单订单数据流追踪 - 第4步：简化渠道检测
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 🔍 开始单订单渠道检测 {orderData: {…}, originalText: 'Joshua: 接机：\n\n团号：EJBTBY250712-1\n2PAX\n13/7 KLIA IN 0…(MF857) - MOXY PUTRAJAYA\n客人：朱芸 \n客人联系：18130403306\n'}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 开始渠道检测 {inputLength: 103}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 未检测到特定渠道 
logger.js?v=2.4.2:493 [6:27:05 PM] [SUCCESS] ✅ 统一渠道检测完成 {channel: null, confidence: 0, method: 'no_match'}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 开始从当前订单状态更新表单 
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 🚀 启用实时填充模式（禁用动画） 
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 开始填充表单数据 {dataKeys: '[FILTERED]'}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 字段映射: customer_name → customer_name {value: '朱芸', hasElement: true}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 字段映射: customer_contact → customer_contact {value: '18130403306', hasElement: true}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 字段映射: customer_email → customer_email {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 字段映射: ota_reference_number → ota_reference_number {value: 'EJBTBY250712-1', hasElement: true}
logger.js?v=2.4.2:496 [6:27:05 PM] [INFO] 字段映射: flight_info → flight_info {value: 'MF857', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: date → date {value: '2024-07-13', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: time → time {value: '01:00', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: pickup → pickup {value: 'Kuala Lumpur International Airport (KLIA1)', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: destination → destination {value: 'Moxy Putrajaya', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: car_type_id → car_type_id {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: sub_category_id → sub_category_id {value: 2, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: driving_region_id → driving_region_id {value: 1, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: passenger_number → passenger_number {value: 2, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: luggage_number → luggage_number {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: extra_requirement → extra_requirement {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: ota_price → ota_price {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: currency → currency {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: incharge_by_backend_user_id → incharge_by_backend_user_id {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [DEBUG] [VehicleConfigManager] 车型推荐完成 {passengerCount: 2, otaChannel: null, recommendedCarTypeId: 5, recommendedName: '5 Seater'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 使用车型配置管理器推荐车型 {passengerCount: 2, recommendedCarTypeId: 5, recommendedName: '5 Seater', description: '推荐给2位乘客的5 Seater'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 下拉框设置成功: car_type_id = 5 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 已应用推荐车型: 5 
logger.js?v=2.4.2:496 [6:27:06 PM] [DEBUG] 🔍 form-manager: applySmartDefaults 中需要设置负责人ID {currentInchargeValue: null, hasElement: true, timestamp: '2025-09-07T10:27:06.090Z'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🔍 getDefaultBackendUserId 开始执行 [object Object] 
logger.js?v=2.4.2:222 🔍 getDefaultBackendUserId 开始执行 {currentUser: {…}, timestamp: '2025-09-07T10:27:06.095Z', localStorageEmail: null, appStateAuth: {…}, completeUserList: '45 users available'}
logger.js?v=2.4.2:496 [6:27:06 PM] [DEBUG] 🔍 第1层映射：检查系统数据 backendUsers {currentUserEmail: '<EMAIL>', backendUsersCount: 44, backendUsersSample: Array(3)}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] ✅ 第1层映射成功：根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 2766, backendUserName: 'demo', matchedUser: {…}, matchType: 'exact', …}
logger.js?v=2.4.2:496 [6:27:06 PM] [DEBUG] 🔍 form-manager: applySmartDefaults 获取默认负责人ID {defaultBackendUserId: 2766, elementId: 'incharge_by_backend_user_id'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: incharge_by_backend_user_id = 2766 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] ✅ form-manager: applySmartDefaults 已应用默认负责人: 2766 {userId: 2766, elementId: 'incharge_by_backend_user_id'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🚀 实时填充模式已完成 
logger.js?v=2.4.2:493 [6:27:06 PM] [SUCCESS] 表单数据填充完成 {fieldsProcessed: 24, realtimeMode: false}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🚀 触发地址处理流水线 (简化版) {hasPickup: true, hasDropoff: true, architecture: 'Gemini + 本地数据库'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] [DependencyContainer] 已创建服务实例: formManager 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🚀 启用实时填充模式（禁用动画） 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 开始填充表单数据 {dataKeys: '[FILTERED]'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: customer_name → customer_name {value: '朱芸', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: customer_contact → customer_contact {value: '18130403306', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: customer_email → customer_email {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: ota_reference_number → ota_reference_number {value: 'EJBTBY250712-1', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: flight_info → flight_info {value: 'MF857', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: date → date {value: '2024-07-13', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: time → time {value: '01:00', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: pickup → pickup {value: 'Kuala Lumpur International Airport (KLIA1)', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: destination → destination {value: 'Moxy Putrajaya', hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: car_type_id → car_type_id {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: sub_category_id → sub_category_id {value: 2, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: driving_region_id → driving_region_id {value: 1, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: passenger_number → passenger_number {value: 2, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: luggage_number → luggage_number {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: extra_requirement → extra_requirement {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: ota_price → ota_price {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: currency → currency {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段映射: incharge_by_backend_user_id → incharge_by_backend_user_id {value: null, hasElement: true}
logger.js?v=2.4.2:496 [6:27:06 PM] [DEBUG] [VehicleConfigManager] 车型推荐完成 {passengerCount: 2, otaChannel: null, recommendedCarTypeId: 5, recommendedName: '5 Seater'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 使用车型配置管理器推荐车型 {passengerCount: 2, recommendedCarTypeId: 5, recommendedName: '5 Seater', description: '推荐给2位乘客的5 Seater'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 下拉框设置成功: car_type_id = 5 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 已应用推荐车型: 5 
logger.js?v=2.4.2:496 [6:27:06 PM] [DEBUG] 🔍 form-manager: applySmartDefaults 中需要设置负责人ID {currentInchargeValue: null, hasElement: true, timestamp: '2025-09-07T10:27:06.315Z'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🔍 getDefaultBackendUserId 开始执行 [object Object] 
logger.js?v=2.4.2:222 🔍 getDefaultBackendUserId 开始执行 {currentUser: {…}, timestamp: '2025-09-07T10:27:06.321Z', localStorageEmail: null, appStateAuth: {…}, completeUserList: '45 users available'}
logger.js?v=2.4.2:496 [6:27:06 PM] [DEBUG] 🔍 第1层映射：检查系统数据 backendUsers {currentUserEmail: '<EMAIL>', backendUsersCount: 44, backendUsersSample: Array(3)}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] ✅ 第1层映射成功：根据登录邮箱匹配到后台用户 {email: '<EMAIL>', backendUserId: 2766, backendUserName: 'demo', matchedUser: {…}, matchType: 'exact', …}
logger.js?v=2.4.2:496 [6:27:06 PM] [DEBUG] 🔍 form-manager: applySmartDefaults 获取默认负责人ID {defaultBackendUserId: 2766, elementId: 'incharge_by_backend_user_id'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: incharge_by_backend_user_id = 2766 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] ✅ form-manager: applySmartDefaults 已应用默认负责人: 2766 {userId: 2766, elementId: 'incharge_by_backend_user_id'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🚀 实时填充模式已完成 
logger.js?v=2.4.2:493 [6:27:06 PM] [SUCCESS] 表单数据填充完成 {fieldsProcessed: 24, realtimeMode: false}
logger.js?v=2.4.2:493 [6:27:06 PM] [SUCCESS] 🚀 实时表单填充完成 
logger.js?v=2.4.2:493 [6:27:06 PM] [SUCCESS] 实时分析完成 {confidence: 30, dataKeys: '[FILTERED]'}
logger.js?v=2.4.2:493 [6:27:06 PM] [SUCCESS] ✅ 单订单处理完成（简化渠道检测） 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🔄 实时分析处理完成 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🚀 开始批量执行18个DOM更新操作 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: customer_name = 朱芸 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: customer_contact = 18130403306 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: customer_email = null 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: ota_reference_number = EJBTBY250712-1 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: flight_info = MF857 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: date = 2024-07-13 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 日期字段填充: date {original: '2024-07-13', formatted: '2024-07-13'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: time = 01:00 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 时间字段填充: time {original: '01:00', formatted: '01:00', priority: 4}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: pickup = Kuala Lumpur International Airport (KLIA1) 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: destination = Moxy Putrajaya 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 下拉框选项不存在: car_type_id = null {availableOptions: Array(19)}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 下拉框设置成功: sub_category_id = 2 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 下拉框设置成功: driving_region_id = 1 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: passenger_number = 2 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: luggage_number = null 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: extra_requirement = null 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: ota_price = null 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 下拉框选项不存在: currency = null {availableOptions: Array(4)}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: incharge_by_backend_user_id = null 
logger.js?v=2.4.2:493 [6:27:06 PM] [SUCCESS] 🚀 批量DOM更新完成 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🚀 开始批量执行18个DOM更新操作 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: customer_name = 朱芸 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: customer_contact = 18130403306 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: customer_email = null 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: ota_reference_number = EJBTBY250712-1 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: flight_info = MF857 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: date = 2024-07-13 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 日期字段填充: date {original: '2024-07-13', formatted: '2024-07-13'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: time = 01:00 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 时间字段填充: time {original: '01:00', formatted: '01:00', priority: 4}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: pickup = Kuala Lumpur International Airport (KLIA1) 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: destination = Moxy Putrajaya 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 下拉框选项不存在: car_type_id = null {availableOptions: Array(19)}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 下拉框设置成功: sub_category_id = 2 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 下拉框设置成功: driving_region_id = 1 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: passenger_number = 2 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: luggage_number = null 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: extra_requirement = null 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: ota_price = null 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 下拉框选项不存在: currency = null {availableOptions: Array(4)}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 字段设置成功: incharge_by_backend_user_id = null 
logger.js?v=2.4.2:493 [6:27:06 PM] [SUCCESS] 🚀 批量DOM更新完成 
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 开始异步处理地址列表 {addressCount: 2}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🚀 开始处理地址 {address: 'Kuala Lumpur International Airport (KLIA1)', options: {…}}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🔍 本地映射分析 {length: 42}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] ❌ 映射分析完成 {confidence: '0%', matched: 0, time: '15ms'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🤖 调用Gemini API {address: 'Kuala Lumpur International Airport (KLIA1)', promptLength: 186912}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 开始调用Gemini API {type: 'text', promptLength: 186912, isRealtime: false}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🚀 开始处理地址 {address: 'Moxy Putrajaya', options: {…}}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🔍 本地映射分析 {length: 14}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] ❌ 映射分析完成 {confidence: '0%', matched: 0, time: '12ms'}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 🤖 调用Gemini API {address: 'Moxy Putrajaya', promptLength: 186884}
logger.js?v=2.4.2:496 [6:27:06 PM] [INFO] 开始调用Gemini API {type: 'text', promptLength: 186884, isRealtime: false}
logger.js?v=2.4.2:496 [6:27:09 PM] [INFO] 实时分析已禁用 
logger.js?v=2.4.2:493 [6:27:09 PM] [SUCCESS] Gemini API调用成功: 3049.00ms {timeout: 25000, model: 'gemini-2.5-flash-lite'}
logger.js?v=2.4.2:496 [6:27:09 PM] [INFO] 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n    \"standardizedAddress\": \"Kuala Lumpur International Airport\",\n    \"confidence\": 0.9,\n    \"metadata\": {\n        \"originalLanguage\": \"Chinese\",\n        \"targetLanguage\": \"English\",\n        \"hasHotelName\": false,\n        \"hasLandmark\": false,\n        \"location\": \"Malaysia\"\n    }\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 73735,
    "candidatesTokenCount": 80,
    "totalTokenCount": 73815,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 73735
      }
    ]
  },
  "modelVersion": "gemini-2.5-flash-lite",
  "responseId": "_l29aPubDuukqtsPtr3G2A0"
} 
logger.js?v=2.4.2:222 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n    \"standardizedAddress\": \"Kuala Lumpur International Airport\",\n    \"confidence\": 0.9,\n    \"metadata\": {\n        \"originalLanguage\": \"Chinese\",\n        \"targetLanguage\": \"English\",\n        \"hasHotelName\": false,\n        \"hasLandmark\": false,\n        \"location\": \"Malaysia\"\n    }\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 73735,
    "candidatesTokenCount": 80,
    "totalTokenCount": 73815,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 73735
      }
    ]
  },
  "modelVersion": "gemini-2.5-flash-lite",
  "responseId": "_l29aPubDuukqtsPtr3G2A0"
}
logger.js?v=2.4.2:496 [6:27:09 PM] [INFO] 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n    \"standardizedAddress\": \"Kuala Lumpur International Airport\",\n    \"confidence\": 0.9,\n    \"metadata\": {\n        \"originalLanguage\": \"Chinese\",\n        \"targetLanguage\": \"English\",\n        \"hasHotelName\": false,\n        \"hasLandmark\": false,\n        \"location\": \"Malaysia\"\n    }\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
} 
logger.js?v=2.4.2:222 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n    \"standardizedAddress\": \"Kuala Lumpur International Airport\",\n    \"confidence\": 0.9,\n    \"metadata\": {\n        \"originalLanguage\": \"Chinese\",\n        \"targetLanguage\": \"English\",\n        \"hasHotelName\": false,\n        \"hasLandmark\": false,\n        \"location\": \"Malaysia\"\n    }\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
}
logger.js?v=2.4.2:493 [6:27:09 PM] [SUCCESS] Gemini API HTTP调用成功 {type: 'text', hasResult: true, resultType: 'object', hasStandardizedAddress: true}
logger.js?v=2.4.2:493 [6:27:09 PM] [SUCCESS] ✅ Gemini翻译成功 {address: 'Kuala Lumpur International Airport (KLIA1)', source: 'gemini_ai', processingTime: 3144}
logger.js?v=2.4.2:496 [6:27:09 PM] [INFO] 🔄 开始更新地址字段 {fieldName: 'pickup', type: 'pickup', hasElement: true, originalAddress: 'Kuala Lumpur International Airport (KLIA1)', processedAddress: 'Kuala Lumpur International Airport', …}
logger.js?v=2.4.2:496 [6:27:09 PM] [INFO] 📝 准备更新字段值 {fieldName: 'pickup', oldValue: 'Kuala Lumpur International Airport (KLIA1)', newValue: 'Kuala Lumpur International Airport', valueChanged: true}
logger.js?v=2.4.2:493 [6:27:09 PM] [SUCCESS] ✅ 地址字段更新完成 {fieldName: 'pickup', updated: true, finalValue: 'Kuala Lumpur International Airport'}
logger.js?v=2.4.2:493 [6:27:09 PM] [SUCCESS] 地址字段已更新 {fieldName: 'pickup', oldValue: 'Kuala Lumpur International Airport (KLIA1)', newValue: 'Kuala Lumpur International Airport'}
logger.js?v=2.4.2:493 [6:27:10 PM] [SUCCESS] Gemini API调用成功: 3323.30ms {timeout: 25000, model: 'gemini-2.5-flash-lite'}
logger.js?v=2.4.2:496 [6:27:10 PM] [INFO] 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n    \"standardizedAddress\": \"Moxy Putrajaya\",\n    \"confidence\": 0.9,\n    \"metadata\": {\n        \"originalLanguage\": \"Chinese\",\n        \"targetLanguage\": \"English\",\n        \"hasHotelName\": true,\n        \"hasLandmark\": false,\n        \"location\": \"Malaysia\"\n    }\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 73731,
    "candidatesTokenCount": 80,
    "totalTokenCount": 73811,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 73731
      }
    ]
  },
  "modelVersion": "gemini-2.5-flash-lite",
  "responseId": "_l29aMXFI-qVmtkP4PePkA4"
} 
logger.js?v=2.4.2:222 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n    \"standardizedAddress\": \"Moxy Putrajaya\",\n    \"confidence\": 0.9,\n    \"metadata\": {\n        \"originalLanguage\": \"Chinese\",\n        \"targetLanguage\": \"English\",\n        \"hasHotelName\": true,\n        \"hasLandmark\": false,\n        \"location\": \"Malaysia\"\n    }\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 73731,
    "candidatesTokenCount": 80,
    "totalTokenCount": 73811,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 73731
      }
    ]
  },
  "modelVersion": "gemini-2.5-flash-lite",
  "responseId": "_l29aMXFI-qVmtkP4PePkA4"
}
logger.js?v=2.4.2:496 [6:27:10 PM] [INFO] 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n    \"standardizedAddress\": \"Moxy Putrajaya\",\n    \"confidence\": 0.9,\n    \"metadata\": {\n        \"originalLanguage\": \"Chinese\",\n        \"targetLanguage\": \"English\",\n        \"hasHotelName\": true,\n        \"hasLandmark\": false,\n        \"location\": \"Malaysia\"\n    }\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
} 
logger.js?v=2.4.2:222 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n    \"standardizedAddress\": \"Moxy Putrajaya\",\n    \"confidence\": 0.9,\n    \"metadata\": {\n        \"originalLanguage\": \"Chinese\",\n        \"targetLanguage\": \"English\",\n        \"hasHotelName\": true,\n        \"hasLandmark\": false,\n        \"location\": \"Malaysia\"\n    }\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
}
logger.js?v=2.4.2:493 [6:27:10 PM] [SUCCESS] Gemini API HTTP调用成功 {type: 'text', hasResult: true, resultType: 'object', hasStandardizedAddress: true}
logger.js?v=2.4.2:493 [6:27:10 PM] [SUCCESS] ✅ Gemini翻译成功 {address: 'Moxy Putrajaya', source: 'gemini_ai', processingTime: 3406}
logger.js?v=2.4.2:493 [6:27:10 PM] [SUCCESS] 地址处理流水线完成 {successCount: 2, totalCount: 2, successRate: '100%'}
logger.js?v=2.4.2:496 [6:27:14 PM] [INFO] 实时分析已启用 
logger.js?v=2.4.2:496 [6:27:15 PM] [INFO] 🌐 检测到中文内容，已自动设置中文语言要求 {sourceField: 'unknown', textLength: 776, hasChinese: true, languageIds: Array(1)}
logger.js?v=2.4.2:496 [6:27:15 PM] [INFO] ⚠️ 检测到重复输入调用，跳过处理 {timeDiff: 90, isPasteEvent: true}
logger.js?v=2.4.2:496 [6:27:15 PM] [INFO] 🌐 检测到中文内容，已自动设置中文语言要求 {sourceField: '#orderInput', textLength: 776, hasChinese: true, languageIds: Array(1)}
logger.js?v=2.4.2:496 [6:27:15 PM] [INFO] 🌐 检测到中文内容，已自动设置中文语言要求 {sourceField: '#orderInput', textLength: 776, hasChinese: true, languageIds: Array(1)}
realtime-analysis-manager.js?v=2.4.2:415 🔍 多订单数据流追踪 - 第1步：实时分析触发
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 输入文本长度: 776 
logger.js?v=2.4.2:222 输入文本长度: 776
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 输入文本预览: [2025/7/10 17:27] Joshua: 接机：

团号：EJBTBY250712-1
2PAX
13/7 KLIA IN 0100 (MF857) - MOXY PUTRAJAYA
客人：朱芸 
客人联系：18130403306
[2025/7/11 19:22] Joshua: 送机：

团号：EJBTBY250710-1
2PAX
14/7 THE FACE STYLE HOTEL... 
logger.js?v=2.4.2:222 输入文本预览: [2025/7/10 17:27] Joshua: 接机：

团号：EJBTBY250712-1
2PAX
13/7 KLIA IN 0100 (MF857) - MOXY PUTRAJAYA
客人：朱芸 
客人联系：18130403306
[2025/7/11 19:22] Joshua: 送机：

团号：EJBTBY250710-1
2PAX
14/7 THE FACE STYLE HOTEL...
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 每次输入都进行新的渠道检测 {inputLength: 776, cacheDisabled: true}
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 渠道检测器已初始化 
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 开始渠道检测 {inputLength: 776}
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 未检测到特定渠道 
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 实时渠道检测完成 {channel: null, confidence: 0, method: 'no_match'}
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 🔍 获取到的渠道检测结果: [object Object] 
logger.js?v=2.4.2:222 🔍 获取到的渠道检测结果: {channel: null, confidence: 0, method: 'no_match'}
realtime-analysis-manager.js?v=2.4.2:449 🔍 多订单数据流追踪 - 第2步：调用Gemini解析
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 🔄 开始实时订单解析... 
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 适配器：解析订单文本 {textLength: 776, isRealtime: true, hasChannelInfo: true}
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 开始处理输入 {type: 'text', inputLength: 776, autoTriggered: false, sourceField: 'unknown'}
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 预检测结果无效，进行新的渠道检测 {invalidResult: {…}}
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 开始渠道检测 {inputLength: 776}
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 未检测到特定渠道 
logger.js?v=2.4.2:496 [6:27:16 PM] [DEBUG] 🔧 [调试] 开始构建提示词 {channel: null, confidence: 0, hasStrategy: false}
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 开始构建提示词 {channel: null, inputLength: 776, autoTriggered: false, sourceField: 'unknown'}
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 使用通用提示词 
logger.js?v=2.4.2:496 [6:27:16 PM] [DEBUG] 🔧 [调试] 提示词构建完成 {promptLength: 6516, containsChannelInfo: true}
logger.js?v=2.4.2:496 [6:27:16 PM] [INFO] 开始调用Gemini API {type: 'text', promptLength: 6516, isRealtime: true}
logger.js?v=2.4.2:493 [6:27:20 PM] [SUCCESS] Gemini API调用成功: 3858.20ms {timeout: 25000, model: 'gemini-2.5-flash-lite'}
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"isMultiOrder\": true,\n  \"orderCount\": 4,\n  \"orders\": [\n    {\n      \"customer_name\": \"朱芸\",\n      \"customer_contact\": \"18130403306\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250712-1\",\n      \"flight_info\": \"MF857\",\n      \"departure_time\": null,\n      \"arrival_time\": \"01:00\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-13\",\n      \"time\": \"01:00\",\n      \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"刘凯\",\n      \"customer_contact\": \"18764221412\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250710-1\",\n      \"flight_info\": \"AK5136\",\n      \"departure_time\": \"05:00\",\n      \"arrival_time\": null,\n      \"flight_type\": \"Departure\",\n      \"date\": \"2025-07-14\",\n      \"time\": \"05:00\",\n      \"pickup\": \"The Face Style Hotel KL\",\n      \"destination\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 3,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"顾婉婷 & 苟晓琼\",\n      \"customer_contact\": \"13884407028\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250715\",\n      \"flight_info\": \"AK181\",\n      \"departure_time\": null,\n      \"arrival_time\": \"20:15\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-15\",\n      \"time\": \"20:15\",\n      \"pickup\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"Rachel tan\",\n      \"customer_contact\": \"+852 6773 0603\",\n      \"customer_email\": \"<EMAIL>\",\n      \"ota\": \"Wilson\",\n      \"ota_reference_number\": null,\n      \"flight_info\": \"CX729\",\n      \"departure_time\": null,\n      \"arrival_time\": \"20:45\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-31\",\n      \"time\": \"20:45\",\n      \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n      \"destination\": \"111A Jalan Sepah Puteri 5/3 Seri Utama 47810 Kota Damansara\",\n      \"passenger_number\": 1,\n      \"luggage_number\": 1,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": true,\n      \"needs_paging_service\": true,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": \"VIP guest pls assign good service driver\"\n    }\n  ],\n  \"confidence\": null\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 3188,
    "candidatesTokenCount": 1262,
    "totalTokenCount": 4450,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 3188
      }
    ]
  },
  "modelVersion": "gemini-2.5-flash-lite",
  "responseId": "CF69aMWAG92jqtsP4ILT4QI"
} 
logger.js?v=2.4.2:222 🔍 Gemini API完整响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"isMultiOrder\": true,\n  \"orderCount\": 4,\n  \"orders\": [\n    {\n      \"customer_name\": \"朱芸\",\n      \"customer_contact\": \"18130403306\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250712-1\",\n      \"flight_info\": \"MF857\",\n      \"departure_time\": null,\n      \"arrival_time\": \"01:00\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-13\",\n      \"time\": \"01:00\",\n      \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"刘凯\",\n      \"customer_contact\": \"18764221412\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250710-1\",\n      \"flight_info\": \"AK5136\",\n      \"departure_time\": \"05:00\",\n      \"arrival_time\": null,\n      \"flight_type\": \"Departure\",\n      \"date\": \"2025-07-14\",\n      \"time\": \"05:00\",\n      \"pickup\": \"The Face Style Hotel KL\",\n      \"destination\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 3,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"顾婉婷 & 苟晓琼\",\n      \"customer_contact\": \"13884407028\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250715\",\n      \"flight_info\": \"AK181\",\n      \"departure_time\": null,\n      \"arrival_time\": \"20:15\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-15\",\n      \"time\": \"20:15\",\n      \"pickup\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"Rachel tan\",\n      \"customer_contact\": \"+852 6773 0603\",\n      \"customer_email\": \"<EMAIL>\",\n      \"ota\": \"Wilson\",\n      \"ota_reference_number\": null,\n      \"flight_info\": \"CX729\",\n      \"departure_time\": null,\n      \"arrival_time\": \"20:45\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-31\",\n      \"time\": \"20:45\",\n      \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n      \"destination\": \"111A Jalan Sepah Puteri 5/3 Seri Utama 47810 Kota Damansara\",\n      \"passenger_number\": 1,\n      \"luggage_number\": 1,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": true,\n      \"needs_paging_service\": true,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": \"VIP guest pls assign good service driver\"\n    }\n  ],\n  \"confidence\": null\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 3188,
    "candidatesTokenCount": 1262,
    "totalTokenCount": 4450,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 3188
      }
    ]
  },
  "modelVersion": "gemini-2.5-flash-lite",
  "responseId": "CF69aMWAG92jqtsP4ILT4QI"
}
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n  \"isMultiOrder\": true,\n  \"orderCount\": 4,\n  \"orders\": [\n    {\n      \"customer_name\": \"朱芸\",\n      \"customer_contact\": \"18130403306\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250712-1\",\n      \"flight_info\": \"MF857\",\n      \"departure_time\": null,\n      \"arrival_time\": \"01:00\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-13\",\n      \"time\": \"01:00\",\n      \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"刘凯\",\n      \"customer_contact\": \"18764221412\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250710-1\",\n      \"flight_info\": \"AK5136\",\n      \"departure_time\": \"05:00\",\n      \"arrival_time\": null,\n      \"flight_type\": \"Departure\",\n      \"date\": \"2025-07-14\",\n      \"time\": \"05:00\",\n      \"pickup\": \"The Face Style Hotel KL\",\n      \"destination\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 3,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"顾婉婷 & 苟晓琼\",\n      \"customer_contact\": \"13884407028\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250715\",\n      \"flight_info\": \"AK181\",\n      \"departure_time\": null,\n      \"arrival_time\": \"20:15\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-15\",\n      \"time\": \"20:15\",\n      \"pickup\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"Rachel tan\",\n      \"customer_contact\": \"+852 6773 0603\",\n      \"customer_email\": \"<EMAIL>\",\n      \"ota\": \"Wilson\",\n      \"ota_reference_number\": null,\n      \"flight_info\": \"CX729\",\n      \"departure_time\": null,\n      \"arrival_time\": \"20:45\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-31\",\n      \"time\": \"20:45\",\n      \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n      \"destination\": \"111A Jalan Sepah Puteri 5/3 Seri Utama 47810 Kota Damansara\",\n      \"passenger_number\": 1,\n      \"luggage_number\": 1,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": true,\n      \"needs_paging_service\": true,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": \"VIP guest pls assign good service driver\"\n    }\n  ],\n  \"confidence\": null\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
} 
logger.js?v=2.4.2:222 🔍 第一个候选结果: {
  "content": {
    "parts": [
      {
        "text": "{\n  \"isMultiOrder\": true,\n  \"orderCount\": 4,\n  \"orders\": [\n    {\n      \"customer_name\": \"朱芸\",\n      \"customer_contact\": \"18130403306\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250712-1\",\n      \"flight_info\": \"MF857\",\n      \"departure_time\": null,\n      \"arrival_time\": \"01:00\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-13\",\n      \"time\": \"01:00\",\n      \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"刘凯\",\n      \"customer_contact\": \"18764221412\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250710-1\",\n      \"flight_info\": \"AK5136\",\n      \"departure_time\": \"05:00\",\n      \"arrival_time\": null,\n      \"flight_type\": \"Departure\",\n      \"date\": \"2025-07-14\",\n      \"time\": \"05:00\",\n      \"pickup\": \"The Face Style Hotel KL\",\n      \"destination\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 3,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"顾婉婷 & 苟晓琼\",\n      \"customer_contact\": \"13884407028\",\n      \"customer_email\": null,\n      \"ota\": null,\n      \"ota_reference_number\": \"EJBTBY250715\",\n      \"flight_info\": \"AK181\",\n      \"departure_time\": null,\n      \"arrival_time\": \"20:15\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-15\",\n      \"time\": \"20:15\",\n      \"pickup\": \"Kuala Lumpur International Airport 2 (KLIA2)\",\n      \"destination\": \"Moxy Putrajaya\",\n      \"passenger_number\": 2,\n      \"luggage_number\": null,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": null,\n      \"needs_paging_service\": null,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": null\n    },\n    {\n      \"customer_name\": \"Rachel tan\",\n      \"customer_contact\": \"+852 6773 0603\",\n      \"customer_email\": \"<EMAIL>\",\n      \"ota\": \"Wilson\",\n      \"ota_reference_number\": null,\n      \"flight_info\": \"CX729\",\n      \"departure_time\": null,\n      \"arrival_time\": \"20:45\",\n      \"flight_type\": \"Arrival\",\n      \"date\": \"2025-07-31\",\n      \"time\": \"20:45\",\n      \"pickup\": \"Kuala Lumpur International Airport (KLIA1)\",\n      \"destination\": \"111A Jalan Sepah Puteri 5/3 Seri Utama 47810 Kota Damansara\",\n      \"passenger_number\": 1,\n      \"luggage_number\": 1,\n      \"sub_category_id\": 2,\n      \"car_type_id\": null,\n      \"driving_region_id\": 1,\n      \"baby_chair\": null,\n      \"tour_guide\": null,\n      \"meet_and_greet\": true,\n      \"needs_paging_service\": true,\n      \"ota_price\": null,\n      \"currency\": null,\n      \"extra_requirement\": \"VIP guest pls assign good service driver\"\n    }\n  ],\n  \"confidence\": null\n}"
      }
    ],
    "role": "model"
  },
  "finishReason": "STOP",
  "index": 0
}
logger.js?v=2.4.2:493 [6:27:20 PM] [SUCCESS] Gemini API HTTP调用成功 {type: 'text', hasResult: true, resultType: 'object', hasStandardizedAddress: false}
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] 开始处理结果 {hasGeminiResult: true, channel: null}
logger.js?v=2.4.2:493 [6:27:20 PM] [SUCCESS] 结果处理完成 {type: 'multi-order', orderCount: 4}
logger.js?v=2.4.2:493 [6:27:20 PM] [SUCCESS] 输入处理完成 {channel: null, resultType: 'multi-order', autoTriggered: false}
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] [GeminiServiceAdapter] 🔧 业务流控制器原始结果: [object Object] 
logger.js?v=2.4.2:222 [GeminiServiceAdapter] 🔧 业务流控制器原始结果: {type: 'multi-order', order: null, orders: Array(4), channel: null, confidence: 0.8, …}
gemini-service-adapter.js?v=2.4.2:218 🤖 Gemini parseOrder 返回
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] meta: [object Object] 
logger.js?v=2.4.2:222 meta: {isRealtime: true}
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] data (object): [object Object],[object Object],[object Object],[object Object] 
logger.js?v=2.4.2:222 data (object): (4) [{…}, {…}, {…}, {…}]
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] data (json):
[
  {
    "customer_name": "朱芸",
    "customer_contact": "18130403306",
    "customer_email": null,
    "ota_reference_number": "EJBTBY250712-1",
    "flight_info": "MF857",
    "date": "2025-07-13",
    "time": "01:00",
    "pickup": "Kuala Lumpur International Airport (KLIA1)",
    "destination": "Moxy Putrajaya",
    "car_type_id": null,
    "sub_category_id": 2,
    "driving_region_id": 1,
    "passenger_number": 2,
    "luggage_number": null,
    "languages_id_array": null,
    "extra_requirement": null,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "ota_price": null,
    "currency": null,
    "incharge_by_backend_user_id": null,
    "ota": null,
    "raw_text": null
  },
  {
    "customer_name": "刘凯",
    "customer_contact": "18764221412",
    "customer_email": null,
    "ota_reference_number": "EJBTBY250710-1",
    "flight_info": "AK5136",
    "date": "2025-07-14",
    "time": "05:00",
    "pickup": "The Face Style Hotel KL",
    "destination": "Kuala Lumpur International Airport 2 (KLIA2)",
    "car_type_id": null,
    "sub_category_id": 3,
    "driving_region_id": 1,
    "passenger_number": 2,
    "luggage_number": null,
    "languages_id_array": null,
    "extra_requirement": null,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "ota_price": null,
    "currency": null,
    "incharge_by_backend_user_id": null,
    "ota": null,
    "raw_text": null
  },
  {
    "customer_name": "顾婉婷 & 苟晓琼",
    "customer_contact": "13884407028",
    "customer_email": null,
    "ota_reference_number": "EJBTBY250715",
    "flight_info": "AK181",
    "date": "2025-07-15",
    "time": "20:15",
    "pickup": "Kuala Lumpur International Airport 2 (KLIA2)",
    "destination": "Moxy Putrajaya",
    "car_type_id": null,
    "sub_category_id": 2,
    "driving_region_id": 1,
    "passenger_number": 2,
    "luggage_number": null,
    "languages_id_array": null,
    "extra_requirement": null,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "ota_price": null,
    "currency": null,
    "incharge_by_backend_user_id": null,
    "ota": null,
    "raw_text": null
  },
  {
    "customer_name": "Rachel tan",
    "customer_contact": "+852 6773 0603",
    "customer_email": "<EMAIL>",
    "ota_reference_number": null,
    "flight_info": "CX729",
    "date": "2025-07-31",
    "time": "20:45",
    "pickup": "Kuala Lumpur International Airport (KLIA1)",
    "destination": "111A Jalan Sepah Puteri 5/3 Seri Utama 47810 Kota Damansara",
    "car_type_id": null,
    "sub_category_id": 2,
    "driving_region_id": 1,
    "passenger_number": 1,
    "luggage_number": 1,
    "languages_id_array": null,
    "extra_requirement": "VIP guest pls assign good service driver",
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": true,
    "ota_price": null,
    "currency": null,
    "incharge_by_backend_user_id": null,
    "ota": "Wilson",
    "raw_text": null
  }
] 
logger.js?v=2.4.2:222 data (json):
[
  {
    "customer_name": "朱芸",
    "customer_contact": "18130403306",
    "customer_email": null,
    "ota_reference_number": "EJBTBY250712-1",
    "flight_info": "MF857",
    "date": "2025-07-13",
    "time": "01:00",
    "pickup": "Kuala Lumpur International Airport (KLIA1)",
    "destination": "Moxy Putrajaya",
    "car_type_id": null,
    "sub_category_id": 2,
    "driving_region_id": 1,
    "passenger_number": 2,
    "luggage_number": null,
    "languages_id_array": null,
    "extra_requirement": null,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "ota_price": null,
    "currency": null,
    "incharge_by_backend_user_id": null,
    "ota": null,
    "raw_text": null
  },
  {
    "customer_name": "刘凯",
    "customer_contact": "18764221412",
    "customer_email": null,
    "ota_reference_number": "EJBTBY250710-1",
    "flight_info": "AK5136",
    "date": "2025-07-14",
    "time": "05:00",
    "pickup": "The Face Style Hotel KL",
    "destination": "Kuala Lumpur International Airport 2 (KLIA2)",
    "car_type_id": null,
    "sub_category_id": 3,
    "driving_region_id": 1,
    "passenger_number": 2,
    "luggage_number": null,
    "languages_id_array": null,
    "extra_requirement": null,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "ota_price": null,
    "currency": null,
    "incharge_by_backend_user_id": null,
    "ota": null,
    "raw_text": null
  },
  {
    "customer_name": "顾婉婷 & 苟晓琼",
    "customer_contact": "13884407028",
    "customer_email": null,
    "ota_reference_number": "EJBTBY250715",
    "flight_info": "AK181",
    "date": "2025-07-15",
    "time": "20:15",
    "pickup": "Kuala Lumpur International Airport 2 (KLIA2)",
    "destination": "Moxy Putrajaya",
    "car_type_id": null,
    "sub_category_id": 2,
    "driving_region_id": 1,
    "passenger_number": 2,
    "luggage_number": null,
    "languages_id_array": null,
    "extra_requirement": null,
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": null,
    "ota_price": null,
    "currency": null,
    "incharge_by_backend_user_id": null,
    "ota": null,
    "raw_text": null
  },
  {
    "customer_name": "Rachel tan",
    "customer_contact": "+852 6773 0603",
    "customer_email": "<EMAIL>",
    "ota_reference_number": null,
    "flight_info": "CX729",
    "date": "2025-07-31",
    "time": "20:45",
    "pickup": "Kuala Lumpur International Airport (KLIA1)",
    "destination": "111A Jalan Sepah Puteri 5/3 Seri Utama 47810 Kota Damansara",
    "car_type_id": null,
    "sub_category_id": 2,
    "driving_region_id": 1,
    "passenger_number": 1,
    "luggage_number": 1,
    "languages_id_array": null,
    "extra_requirement": "VIP guest pls assign good service driver",
    "baby_chair": null,
    "tour_guide": null,
    "meet_and_greet": true,
    "ota_price": null,
    "currency": null,
    "incharge_by_backend_user_id": null,
    "ota": "Wilson",
    "raw_text": null
  }
]
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] Gemini parseResult: [object Object],[object Object],[object Object],[object Object] 
logger.js?v=2.4.2:222 Gemini parseResult: (4) [{…}, {…}, {…}, {…}]
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] 🔍 parseOrder返回结果调试 {parseResult: Array(4), isArray: true, length: 4, type: 'object'}
logger.js?v=2.4.2:493 [6:27:20 PM] [SUCCESS] ✅ 解析完成，检测到 4 个订单 
logger.js?v=2.4.2:493 [6:27:20 PM] [SUCCESS] ✅ 检测到 4 个订单 
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] 🔧 构造多订单数据 {orderCount: 4, confidence: 29, source: 'realtime-analysis'}
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] 🚀 准备跳转到多订单页面V2 {orderCount: 4, confidence: 29}
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] 🧭 导航到: /multi-order 
logger.js?v=2.4.2:493 [6:27:20 PM] [SUCCESS] ✅ 通过路由系统跳转到多订单页面 
logger.js?v=2.4.2:493 [6:27:20 PM] [SUCCESS] 🎉 已跳转到多订单页面V2 
logger.js?v=2.4.2:496 [6:27:20 PM] [INFO] 🔄 实时分析处理完成 
