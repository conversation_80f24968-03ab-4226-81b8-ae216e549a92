# 监听器实施指南

## 1. 快速开始

### 1.1 环境准备

确保项目中已安装以下依赖：

```bash
# 检查现有依赖
npm list react
npm list @supabase/supabase-js

# 如果需要，安装额外依赖
npm install lodash.debounce
npm install uuid
```

### 1.2 文件结构

在项目中创建以下文件结构：

```
js/
├── listeners/
│   ├── core/
│   │   ├── listener-registry.js
│   │   ├── error-handler.js
│   │   └── performance-monitor.js
│   ├── form/
│   │   ├── form-validation-listener.js
│   │   └── auto-save-listener.js
│   ├── upload/
│   │   └── drag-drop-upload-listener.js
│   └── utils/
│       ├── debounce.js
│       └── event-utils.js
└── managers/
    └── listener-manager.js
```

## 2. 核心组件实现

### 2.1 监听器注册中心

创建 `js/listeners/core/listener-registry.js`：

```javascript
/**
 * 监听器注册中心
 * @REGISTRY @CORE
 */
class ListenerRegistry {
    constructor() {
        this.listeners = new Map(); // 存储所有注册的监听器
        this.eventQueue = []; // 事件队列
        this.isProcessing = false; // 是否正在处理事件
        this.errorHandler = null; // 错误处理器
        this.performanceMonitor = null; // 性能监控器
        
        // 绑定到全局对象
        if (typeof window !== 'undefined') {
            window.OTA = window.OTA || {};
            window.OTA.listenerRegistry = this;
        }
        
        console.log('[ListenerRegistry] 监听器注册中心已初始化');
    }
    
    /**
     * 注册监听器
     * @param {string} name - 监听器名称
     * @param {Object} listener - 监听器实例
     * @param {number} priority - 优先级（数字越大优先级越高）
     * @param {Array} dependencies - 依赖的其他监听器
     */
    register(name, listener, priority = 0, dependencies = []) {
        try {
            // 检查依赖
            for (const dep of dependencies) {
                if (!this.listeners.has(dep)) {
                    throw new Error(`依赖的监听器 "${dep}" 尚未注册`);
                }
            }
            
            // 注册监听器
            this.listeners.set(name, {
                instance: listener,
                priority,
                dependencies,
                isActive: true,
                registeredAt: Date.now(),
                errorCount: 0,
                lastError: null
            });
            
            // 按优先级重新排序
            this.sortListenersByPriority();
            
            // 初始化监听器
            if (typeof listener.initialize === 'function') {
                listener.initialize();
            }
            
            console.log(`[ListenerRegistry] 监听器 "${name}" 注册成功，优先级: ${priority}`);
            
            // 触发注册事件
            this.dispatchEvent('listenerRegistered', { name, priority });
            
            return true;
        } catch (error) {
            console.error(`[ListenerRegistry] 注册监听器 "${name}" 失败:`, error);
            if (this.errorHandler) {
                this.errorHandler.handleError(name, error, 'registration');
            }
            return false;
        }
    }
    
    /**
     * 注销监听器
     * @param {string} name - 监听器名称
     */
    unregister(name) {
        try {
            const listenerInfo = this.listeners.get(name);
            if (!listenerInfo) {
                console.warn(`[ListenerRegistry] 监听器 "${name}" 不存在`);
                return false;
            }
            
            // 检查是否有其他监听器依赖此监听器
            const dependents = this.findDependents(name);
            if (dependents.length > 0) {
                console.warn(`[ListenerRegistry] 无法注销监听器 "${name}"，以下监听器依赖它: ${dependents.join(', ')}`);
                return false;
            }
            
            // 销毁监听器
            if (typeof listenerInfo.instance.destroy === 'function') {
                listenerInfo.instance.destroy();
            }
            
            // 从注册表中移除
            this.listeners.delete(name);
            
            console.log(`[ListenerRegistry] 监听器 "${name}" 已注销`);
            
            // 触发注销事件
            this.dispatchEvent('listenerUnregistered', { name });
            
            return true;
        } catch (error) {
            console.error(`[ListenerRegistry] 注销监听器 "${name}" 失败:`, error);
            return false;
        }
    }
    
    /**
     * 获取监听器
     * @param {string} name - 监听器名称
     */
    get(name) {
        const listenerInfo = this.listeners.get(name);
        return listenerInfo ? listenerInfo.instance : null;
    }
    
    /**
     * 检查监听器是否存在
     * @param {string} name - 监听器名称
     */
    has(name) {
        return this.listeners.has(name);
    }
    
    /**
     * 获取所有监听器信息
     */
    getAll() {
        const result = {};
        for (const [name, info] of this.listeners) {
            result[name] = {
                priority: info.priority,
                isActive: info.isActive,
                dependencies: info.dependencies,
                registeredAt: info.registeredAt,
                errorCount: info.errorCount
            };
        }
        return result;
    }
    
    /**
     * 按优先级排序监听器
     */
    sortListenersByPriority() {
        const sorted = Array.from(this.listeners.entries())
            .sort(([,a], [,b]) => b.priority - a.priority);
        
        this.listeners = new Map(sorted);
    }
    
    /**
     * 查找依赖指定监听器的其他监听器
     * @param {string} name - 监听器名称
     */
    findDependents(name) {
        const dependents = [];
        for (const [listenerName, info] of this.listeners) {
            if (info.dependencies.includes(name)) {
                dependents.push(listenerName);
            }
        }
        return dependents;
    }
    
    /**
     * 分发事件到所有监听器
     * @param {string} eventType - 事件类型
     * @param {Object} eventData - 事件数据
     */
    dispatchEvent(eventType, eventData = {}) {
        const event = {
            type: eventType,
            data: eventData,
            timestamp: Date.now(),
            id: this.generateEventId()
        };
        
        // 添加到事件队列
        this.eventQueue.push(event);
        
        // 如果没有在处理事件，开始处理
        if (!this.isProcessing) {
            this.processEventQueue();
        }
    }
    
    /**
     * 处理事件队列
     */
    async processEventQueue() {
        this.isProcessing = true;
        
        while (this.eventQueue.length > 0) {
            const event = this.eventQueue.shift();
            await this.processEvent(event);
        }
        
        this.isProcessing = false;
    }
    
    /**
     * 处理单个事件
     * @param {Object} event - 事件对象
     */
    async processEvent(event) {
        const startTime = performance.now();
        
        for (const [name, info] of this.listeners) {
            if (!info.isActive) continue;
            
            try {
                const listener = info.instance;
                
                // 检查监听器是否有对应的事件处理方法
                const handlerName = `on${this.capitalizeFirst(event.type)}`;
                if (typeof listener[handlerName] === 'function') {
                    await listener[handlerName](event.data, event);
                }
                
                // 通用事件处理方法
                if (typeof listener.handleEvent === 'function') {
                    await listener.handleEvent(event.type, event.data, event);
                }
                
            } catch (error) {
                console.error(`[ListenerRegistry] 监听器 "${name}" 处理事件 "${event.type}" 时发生错误:`, error);
                
                // 增加错误计数
                info.errorCount++;
                info.lastError = {
                    message: error.message,
                    timestamp: Date.now(),
                    eventType: event.type
                };
                
                // 如果错误次数过多，暂停监听器
                if (info.errorCount >= 5) {
                    info.isActive = false;
                    console.warn(`[ListenerRegistry] 监听器 "${name}" 因错误过多被暂停`);
                }
                
                // 使用错误处理器
                if (this.errorHandler) {
                    this.errorHandler.handleError(name, error, 'eventProcessing');
                }
            }
        }
        
        const endTime = performance.now();
        const processingTime = endTime - startTime;
        
        // 性能监控
        if (this.performanceMonitor) {
            this.performanceMonitor.recordEventProcessing(event.type, processingTime);
        }
        
        // 如果处理时间过长，记录警告
        if (processingTime > 100) {
            console.warn(`[ListenerRegistry] 事件 "${event.type}" 处理时间过长: ${processingTime.toFixed(2)}ms`);
        }
    }
    
    /**
     * 生成事件ID
     */
    generateEventId() {
        return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 首字母大写
     * @param {string} str - 字符串
     */
    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
    
    /**
     * 设置错误处理器
     * @param {Object} errorHandler - 错误处理器实例
     */
    setErrorHandler(errorHandler) {
        this.errorHandler = errorHandler;
    }
    
    /**
     * 设置性能监控器
     * @param {Object} performanceMonitor - 性能监控器实例
     */
    setPerformanceMonitor(performanceMonitor) {
        this.performanceMonitor = performanceMonitor;
    }
    
    /**
     * 重新激活监听器
     * @param {string} name - 监听器名称
     */
    reactivate(name) {
        const listenerInfo = this.listeners.get(name);
        if (listenerInfo) {
            listenerInfo.isActive = true;
            listenerInfo.errorCount = 0;
            listenerInfo.lastError = null;
            console.log(`[ListenerRegistry] 监听器 "${name}" 已重新激活`);
            return true;
        }
        return false;
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        const stats = {
            totalListeners: this.listeners.size,
            activeListeners: 0,
            inactiveListeners: 0,
            totalErrors: 0,
            queueLength: this.eventQueue.length,
            isProcessing: this.isProcessing
        };
        
        for (const [name, info] of this.listeners) {
            if (info.isActive) {
                stats.activeListeners++;
            } else {
                stats.inactiveListeners++;
            }
            stats.totalErrors += info.errorCount;
        }
        
        return stats;
    }
    
    /**
     * 销毁注册中心
     */
    destroy() {
        // 注销所有监听器
        for (const [name] of this.listeners) {
            this.unregister(name);
        }
        
        // 清空事件队列
        this.eventQueue = [];
        this.isProcessing = false;
        
        // 清理全局引用
        if (typeof window !== 'undefined' && window.OTA) {
            delete window.OTA.listenerRegistry;
        }
        
        console.log('[ListenerRegistry] 监听器注册中心已销毁');
    }
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.ListenerRegistry = ListenerRegistry;
}

export default ListenerRegistry;
```

### 2.2 错误处理器

创建 `js/listeners/core/error-handler.js`：

```javascript
/**
 * 监听器错误处理器
 * @ERROR_HANDLER @CORE
 */
class ListenerErrorHandler {
    constructor() {
        this.errorCounts = new Map(); // 错误计数
        this.errorHistory = []; // 错误历史
        this.maxRetries = 3; // 最大重试次数
        this.retryDelay = 1000; // 重试延迟（毫秒）
        this.maxHistorySize = 100; // 最大历史记录数
        
        console.log('[ListenerErrorHandler] 错误处理器已初始化');
    }
    
    /**
     * 处理监听器错误
     * @param {string} listenerName - 监听器名称
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     */
    handleError(listenerName, error, context = 'unknown') {
        const errorInfo = {
            listenerName,
            error: {
                message: error.message,
                stack: error.stack,
                name: error.name
            },
            context,
            timestamp: Date.now(),
            id: this.generateErrorId()
        };
        
        // 记录错误
        this.recordError(errorInfo);
        
        // 获取错误计数
        const errorCount = this.errorCounts.get(listenerName) || 0;
        this.errorCounts.set(listenerName, errorCount + 1);
        
        // 错误分类处理
        const errorType = this.classifyError(error);
        
        switch (errorType) {
            case 'critical':
                this.handleCriticalError(errorInfo);
                break;
            case 'recoverable':
                this.handleRecoverableError(errorInfo);
                break;
            case 'warning':
                this.handleWarningError(errorInfo);
                break;
            default:
                this.handleUnknownError(errorInfo);
        }
        
        // 发送错误报告
        this.sendErrorReport(errorInfo);
    }
    
    /**
     * 记录错误
     * @param {Object} errorInfo - 错误信息
     */
    recordError(errorInfo) {
        this.errorHistory.push(errorInfo);
        
        // 限制历史记录大小
        if (this.errorHistory.length > this.maxHistorySize) {
            this.errorHistory.shift();
        }
        
        // 控制台输出
        console.error(`[ListenerErrorHandler] ${errorInfo.listenerName} 错误:`, {
            message: errorInfo.error.message,
            context: errorInfo.context,
            timestamp: new Date(errorInfo.timestamp).toISOString()
        });
    }
    
    /**
     * 错误分类
     * @param {Error} error - 错误对象
     */
    classifyError(error) {
        // 关键错误
        if (error.name === 'SecurityError' || 
            error.message.includes('Permission denied') ||
            error.message.includes('Network error')) {
            return 'critical';
        }
        
        // 可恢复错误
        if (error.name === 'TypeError' ||
            error.name === 'ReferenceError' ||
            error.message.includes('timeout')) {
            return 'recoverable';
        }
        
        // 警告级错误
        if (error.name === 'ValidationError' ||
            error.message.includes('Invalid input')) {
            return 'warning';
        }
        
        return 'unknown';
    }
    
    /**
     * 处理关键错误
     * @param {Object} errorInfo - 错误信息
     */
    handleCriticalError(errorInfo) {
        console.error(`[ListenerErrorHandler] 关键错误 - 监听器 "${errorInfo.listenerName}" 将被禁用`);
        
        // 禁用监听器
        this.disableListener(errorInfo.listenerName);
        
        // 通知用户
        this.notifyUser('系统遇到关键错误，部分功能可能受到影响', 'error');
        
        // 发送紧急报告
        this.sendUrgentReport(errorInfo);
    }
    
    /**
     * 处理可恢复错误
     * @param {Object} errorInfo - 错误信息
     */
    handleRecoverableError(errorInfo) {
        const errorCount = this.errorCounts.get(errorInfo.listenerName) || 0;
        
        if (errorCount < this.maxRetries) {
            console.warn(`[ListenerErrorHandler] 可恢复错误 - 将在 ${this.retryDelay}ms 后重试 (${errorCount}/${this.maxRetries})`);
            
            // 延迟重试
            setTimeout(() => {
                this.retryListener(errorInfo.listenerName);
            }, this.retryDelay * (errorCount + 1));
        } else {
            console.error(`[ListenerErrorHandler] 重试次数已达上限 - 监听器 "${errorInfo.listenerName}" 将被暂停`);
            this.pauseListener(errorInfo.listenerName);
        }
    }
    
    /**
     * 处理警告错误
     * @param {Object} errorInfo - 错误信息
     */
    handleWarningError(errorInfo) {
        console.warn(`[ListenerErrorHandler] 警告错误 - 监听器 "${errorInfo.listenerName}" 继续运行`);
        
        // 记录警告但不影响运行
        this.recordWarning(errorInfo);
    }
    
    /**
     * 处理未知错误
     * @param {Object} errorInfo - 错误信息
     */
    handleUnknownError(errorInfo) {
        console.error(`[ListenerErrorHandler] 未知错误类型 - 监听器 "${errorInfo.listenerName}"`);
        
        // 采用保守策略，暂停监听器
        this.pauseListener(errorInfo.listenerName);
    }
    
    /**
     * 重试监听器
     * @param {string} listenerName - 监听器名称
     */
    retryListener(listenerName) {
        try {
            const registry = window.OTA?.listenerRegistry;
            if (registry) {
                // 重新激活监听器
                registry.reactivate(listenerName);
                console.log(`[ListenerErrorHandler] 监听器 "${listenerName}" 重试成功`);
            }
        } catch (error) {
            console.error(`[ListenerErrorHandler] 重试监听器 "${listenerName}" 失败:`, error);
            this.handleError(listenerName, error, 'retry');
        }
    }
    
    /**
     * 禁用监听器
     * @param {string} listenerName - 监听器名称
     */
    disableListener(listenerName) {
        const registry = window.OTA?.listenerRegistry;
        if (registry) {
            const listenerInfo = registry.listeners?.get(listenerName);
            if (listenerInfo) {
                listenerInfo.isActive = false;
                console.log(`[ListenerErrorHandler] 监听器 "${listenerName}" 已禁用`);
            }
        }
    }
    
    /**
     * 暂停监听器
     * @param {string} listenerName - 监听器名称
     */
    pauseListener(listenerName) {
        this.disableListener(listenerName);
        
        // 设置自动恢复定时器（5分钟后）
        setTimeout(() => {
            this.autoRecover(listenerName);
        }, 5 * 60 * 1000);
    }
    
    /**
     * 自动恢复监听器
     * @param {string} listenerName - 监听器名称
     */
    autoRecover(listenerName) {
        console.log(`[ListenerErrorHandler] 尝试自动恢复监听器 "${listenerName}"`);
        
        // 重置错误计数
        this.errorCounts.set(listenerName, 0);
        
        // 重新激活
        this.retryListener(listenerName);
    }
    
    /**
     * 记录警告
     * @param {Object} errorInfo - 错误信息
     */
    recordWarning(errorInfo) {
        // 可以发送到日志服务
        if (window.OTA?.logger) {
            window.OTA.logger.warn(`监听器警告: ${errorInfo.listenerName}`, errorInfo);
        }
    }
    
    /**
     * 通知用户
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    notifyUser(message, type = 'info') {
        // 使用现有的UI管理器显示消息
        if (window.OTA?.managers?.ui?.showAlert) {
            window.OTA.managers.ui.showAlert(message, type);
        } else {
            // 降级到浏览器原生提示
            if (type === 'error') {
                console.error(message);
            } else {
                console.log(message);
            }
        }
    }
    
    /**
     * 发送错误报告
     * @param {Object} errorInfo - 错误信息
     */
    sendErrorReport(errorInfo) {
        // 这里可以集成错误报告服务，如Sentry
        try {
            // 模拟发送到错误报告服务
            const report = {
                listener: errorInfo.listenerName,
                error: errorInfo.error.message,
                context: errorInfo.context,
                timestamp: errorInfo.timestamp,
                userAgent: navigator.userAgent,
                url: window.location.href
            };
            
            // 实际项目中可以发送到服务器
            console.log('[ListenerErrorHandler] 错误报告:', report);
            
        } catch (error) {
            console.error('[ListenerErrorHandler] 发送错误报告失败:', error);
        }
    }
    
    /**
     * 发送紧急报告
     * @param {Object} errorInfo - 错误信息
     */
    sendUrgentReport(errorInfo) {
        // 紧急错误需要立即处理
        this.sendErrorReport({
            ...errorInfo,
            urgent: true,
            severity: 'critical'
        });
    }
    
    /**
     * 生成错误ID
     */
    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 获取错误统计
     */
    getErrorStats() {
        const stats = {
            totalErrors: this.errorHistory.length,
            errorsByListener: {},
            errorsByType: {},
            recentErrors: this.errorHistory.slice(-10)
        };
        
        // 按监听器统计
        for (const [listener, count] of this.errorCounts) {
            stats.errorsByListener[listener] = count;
        }
        
        // 按类型统计
        for (const errorInfo of this.errorHistory) {
            const type = errorInfo.context;
            stats.errorsByType[type] = (stats.errorsByType[type] || 0) + 1;
        }
        
        return stats;
    }
    
    /**
     * 清理错误历史
     */
    clearErrorHistory() {
        this.errorHistory = [];
        this.errorCounts.clear();
        console.log('[ListenerErrorHandler] 错误历史已清理');
    }
    
    /**
     * 销毁错误处理器
     */
    destroy() {
        this.clearErrorHistory();
        console.log('[ListenerErrorHandler] 错误处理器已销毁');
    }
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.ListenerErrorHandler = ListenerErrorHandler;
}

export default ListenerErrorHandler;
```

### 2.3 表单验证监听器

创建 `js/listeners/form/form-validation-listener.js`：

```javascript
/**
 * 表单验证监听器
 * @LISTENER @FORM_VALIDATION
 */
import { debounce } from '../utils/debounce.js';

class FormValidationListener {
    constructor(formManager) {
        this.formManager = formManager; // 表单管理器引用
        this.validationRules = this.initValidationRules(); // 验证规则
        this.debounceTimers = new Map(); // 防抖定时器
        this.fieldStates = new Map(); // 字段状态
        this.isInitialized = false; // 初始化状态
        
        console.log('[FormValidationListener] 表单验证监听器已创建');
    }
    
    /**
     * 初始化监听器
     */
    initialize() {
        if (this.isInitialized) {
            console.warn('[FormValidationListener] 监听器已经初始化');
            return;
        }
        
        try {
            this.setupFormListeners();
            this.setupGlobalListeners();
            this.isInitialized = true;
            console.log('[FormValidationListener] 监听器初始化完成');
        } catch (error) {
            console.error('[FormValidationListener] 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 初始化验证规则
     */
    initValidationRules() {
        return {
            customerName: {
                required: true,
                minLength: 2,
                maxLength: 50,
                pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/,
                message: '客户姓名必须为2-50个字符的中文或英文'
            },
            contactNumber: {
                required: true,
                pattern: /^[\+]?[1-9][\d]{0,3}[\s]?[\d]{4,14}$/,
                message: '请输入有效的联系电话'
            },
            email: {
                required: false,
                pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: '请输入有效的邮箱地址'
            },
            pickupLocation: {
                required: true,
                minLength: 5,
                maxLength: 200,
                message: '取货地点必须为5-200个字符'
            },
            destination: {
                required: true,
                minLength: 5,
                maxLength: 200,
                message: '目的地必须为5-200个字符'
            },
            pickupDate: {
                required: true,
                custom: this.validateDate.bind(this),
                message: '请选择有效的取货日期'
            },
            pickupTime: {
                required: true,
                pattern: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
                message: '请输入有效的时间格式 (HH:MM)'
            },
            passengerCount: {
                required: true,
                min: 1,
                max: 20,
                type: 'number',
                message: '乘客数量必须在1-20之间'
            },
            luggageCount: {
                required: false,
                min: 0,
                max: 50,
                type: 'number',
                message: '行李数量必须在0-50之间'
            }
        };
    }
    
    /**
     * 设置表单监听器
     */
    setupFormListeners() {
        const form = document.getElementById('orderForm');
        if (!form) {
            throw new Error('找不到订单表单元素');
        }
        
        // 获取所有输入字段
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            const fieldName = input.name;
            if (!fieldName || !this.validationRules[fieldName]) return;
            
            // 实时输入验证（防抖）
            input.addEventListener('input', debounce((e) => {
                this.validateField(fieldName, e.target.value, false);
            }, 300));
            
            // 失焦验证（立即）
            input.addEventListener('blur', (e) => {
                this.validateField(fieldName, e.target.value, true);
            });
            
            // 获得焦点时清除错误
            input.addEventListener('focus', (e) => {
                this.clearFieldError(fieldName);
            });
            
            // 初始化字段状态
            this.fieldStates.set(fieldName, {
                isValid: null,
                lastValue: input.value,
                errorMessage: null,
                lastValidated: null
            });
        });
        
        // 表单提交验证
        form.addEventListener('submit', (e) => {
            if (!this.validateForm()) {
                e.preventDefault();
                this.showFormErrors();
            }
        });
        
        console.log(`[FormValidationListener] 已为 ${inputs.length} 个字段设置监听器`);
    }
    
    /**
     * 设置全局监听器
     */
    setupGlobalListeners() {
        // 监听表单重置事件
        document.addEventListener('formReset', () => {
            this.resetValidation();
        });
        
        // 监听字段动态添加事件
        document.addEventListener('fieldAdded', (e) => {
            const { fieldName, element } = e.detail;
            this.addFieldValidation(fieldName, element);
        });
    }
    
    /**
     * 验证单个字段
     * @param {string} fieldName - 字段名称
     * @param {*} value - 字段值
     * @param {boolean} showFeedback - 是否显示反馈
     */
    validateField(fieldName, value, showFeedback = true) {
        const rules = this.validationRules[fieldName];
        if (!rules) return true;
        
        const errors = [];
        const fieldState = this.fieldStates.get(fieldName) || {};
        
        try {
            // 必填验证
            if (rules.required && this.isEmpty(value)) {
                errors.push(rules.message || `${this.getFieldLabel(fieldName)}为必填项`);
            }
            
            // 如果字段为空且非必填，跳过其他验证
            if (!rules.required && this.isEmpty(value)) {
                this.updateFieldState(fieldName, true, null, value);
                if (showFeedback) {
                    this.clearFieldError(fieldName);
                }
                return true;
            }
            
            // 类型验证
            if (rules.type === 'number') {
                const numValue = Number(value);
                if (isNaN(numValue)) {
                    errors.push(`${this.getFieldLabel(fieldName)}必须是数字`);
                } else {
                    // 数值范围验证
                    if (rules.min !== undefined && numValue < rules.min) {
                        errors.push(`${this.getFieldLabel(fieldName)}不能小于${rules.min}`);
                    }
                    if (rules.max !== undefined && numValue > rules.max) {
                        errors.push(`${this.getFieldLabel(fieldName)}不能大于${rules.max}`);
                    }
                }
            }
            
            // 长度验证
            if (typeof value === 'string') {
                if (rules.minLength && value.length < rules.minLength) {
                    errors.push(`${this.getFieldLabel(fieldName)}至少需要${rules.minLength}个字符`);
                }
                if (rules.maxLength && value.length > rules.maxLength) {
                    errors.push(`${this.getFieldLabel(fieldName)}不能超过${rules.maxLength}个字符`);
                }
            }
            
            // 正则表达式验证
            if (rules.pattern && !this.isEmpty(value) && !rules.pattern.test(value)) {
                errors.push(rules.message || `${this.getFieldLabel(fieldName)}格式不正确`);
            }
            
            // 自定义验证
            if (rules.custom && typeof rules.custom === 'function') {
                const customResult = rules.custom(value, fieldName);
                if (customResult !== true) {
                    errors.push(customResult || rules.message);
                }
            }
            
            // 更新字段状态
            const isValid = errors.length === 0;
            this.updateFieldState(fieldName, isValid, errors[0] || null, value);
            
            // 显示验证反馈
            if (showFeedback) {
                if (isValid) {
                    this.showFieldSuccess(fieldName);
                } else {
                    this.showFieldError(fieldName, errors[0]);
                }
            }
            
            return isValid;
            
        } catch (error) {
            console.error(`[FormValidationListener] 验证字段 "${fieldName}" 时发生错误:`, error);
            return false;
        }
    }
    
    /**
     * 验证整个表单
     */
    validateForm() {
        let isFormValid = true;
        const form = document.getElementById('orderForm');
        
        if (!form) return false;
        
        const formData = new FormData(form);
        
        // 验证所有字段
        for (const [fieldName] of formData.entries()) {
            const fieldValue = formData.get(fieldName);
            if (!this.validateField(fieldName, fieldValue, true)) {
                isFormValid = false;
            }
        }
        
        // 检查必填字段是否都有值
        for (const [fieldName, rules] of Object.entries(this.validationRules)) {
            if (rules.required && !formData.has(fieldName)) {
                this.showFieldError(fieldName, `${this.getFieldLabel(fieldName)}为必填项`);
                isFormValid = false;
            }
        }
        
        // 触发表单验证完成事件
        this.dispatchValidationEvent('formValidated', {
            isValid: isFormValid,
            fieldStates: Object.fromEntries(this.fieldStates)
        });
        
        return isFormValid;
    }
    
    /**
     * 自定义日期验证
     * @param {string} dateValue - 日期值
     */
    validateDate(dateValue) {
        if (!dateValue) return '请选择日期';
        
        const selectedDate = new Date(dateValue);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // 检查日期是否有效
        if (isNaN(selectedDate.getTime())) {
            return '请选择有效的日期';
        }
        
        // 检查是否是过去的日期
        if (selectedDate < today) {
            return '不能选择过去的日期';
        }
        
        // 检查是否超过一年
        const oneYearLater = new Date(today);
        oneYearLater.setFullYear(today.getFullYear() + 1);
        
        if (selectedDate > oneYearLater) {
            return '不能选择一年以后的日期';
        }
        
        return true;
    }
    
    /**
     * 检查值是否为空
     * @param {*} value - 值
     */
    isEmpty(value) {
        return value === null || value === undefined || 
               (typeof value === 'string' && value.trim() === '') ||
               (Array.isArray(value) && value.length === 0);
    }
    
    /**
     * 更新字段状态
     * @param {string} fieldName - 字段名称
     * @param {boolean} isValid - 是否有效
     * @param {string} errorMessage - 错误消息
     * @param {*} value - 字段值
     */
    updateFieldState(fieldName, isValid, errorMessage, value) {
        this.fieldStates.set(fieldName, {
            isValid,
            lastValue: value,
            errorMessage,
            lastValidated: Date.now()
        });
    }
    
    /**
     * 显示字段错误
     * @param {string} fieldName - 字段名称
     * @param {string} message - 错误消息
     */
    showFieldError(fieldName, message) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;
        
        // 添加错误样式
        field.classList.add('error', 'border-red-500');
        field.classList.remove('success', 'border-green-500');
        
        // 显示错误消息
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error text-red-500 text-sm mt-1';
            field.parentNode.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        
        // 触发字段错误事件
        this.dispatchValidationEvent('fieldError', {
            fieldName,
            message,
            element: field
        });
    }
    
    /**
     * 显示字段成功状态
     * @param {string} fieldName - 字段名称
     */
    showFieldSuccess(fieldName) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;
        
        field.classList.add('success', 'border-green-500');
        field.classList.remove('error', 'border-red-500');
        
        // 隐藏错误消息
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
        
        // 触发字段成功事件
        this.dispatchValidationEvent('fieldSuccess', {
            fieldName,
            element: field
        });
    }
    
    /**
     * 清除字段错误
     * @param {string} fieldName - 字段名称
     */
    clearFieldError(fieldName) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;
        
        field.classList.remove('error', 'success', 'border-red-500', 'border-green-500');
        
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }
    
    /**
     * 显示表单错误汇总
     */
    showFormErrors() {
        const errors = [];
        
        for (const [fieldName, state] of this.fieldStates) {
            if (!state.isValid && state.errorMessage) {
                errors.push(`${this.getFieldLabel(fieldName)}: ${state.errorMessage}`);
            }
        }
        
        if (errors.length > 0) {
            const message = '请修正以下错误:\n' + errors.join('\n');
            
            // 使用UI管理器显示错误
            if (window.OTA?.managers?.ui?.showAlert) {
                window.OTA.managers.ui.showAlert(message, 'error');
            } else {
                alert(message);
            }
        }
    }
    
    /**
     * 获取字段标签
     * @param {string} fieldName - 字段名称
     */
    getFieldLabel(fieldName) {
        const labelMap = {
            customerName: '客户姓名',
            contactNumber: '联系电话',
            email: '邮箱地址',
            pickupLocation: '取货地点',
            destination: '目的地',
            pickupDate: '取货日期',
            pickupTime: '取货时间',
            passengerCount: '乘客数量',
            luggageCount: '行李数量'
        };
        return labelMap[fieldName] || fieldName;
    }
    
    /**
     * 重置验证状态
     */
    resetValidation() {
        // 清除所有字段状态
        for (const fieldName of this.fieldStates.keys()) {
            this.clearFieldError(fieldName);
        }
        
        this.fieldStates.clear();
        
        console.log('[FormValidationListener] 验证状态已重置');
    }
    
    /**
     * 添加字段验证
     * @param {string} fieldName - 字段名称
     * @param {HTMLElement} element - 字段元素
     */
    addFieldValidation(fieldName, element) {
        if (!this.validationRules[fieldName]) return;
        
        // 为新字段添加事件监听器
        element.addEventListener('input', debounce((e) => {
            this.validateField(fieldName, e.target.value, false);
        }, 300));
        
        element.addEventListener('blur', (e) => {
            this.validateField(fieldName, e.target.value, true);
        });
        
        element.addEventListener('focus', (e) => {
            this.clearFieldError(fieldName);
        });
        
        console.log(`[FormValidationListener] 已为字段 "${fieldName}" 添加验证`);
    }
    
    /**
     * 分发验证事件
     * @param {string} eventType - 事件类型
     * @param {Object} detail - 事件详情
     */
    dispatchValidationEvent(eventType, detail) {
        const event = new CustomEvent(`validation:${eventType}`, {
            detail,
            bubbles: true
        });
        
        document.dispatchEvent(event);
    }
    
    /**
     * 获取验证统计
     */
    getValidationStats() {
        const stats = {
            totalFields: this.fieldStates.size,
            validFields: 0,
            invalidFields: 0,
            unvalidatedFields: 0
        };
        
        for (const [fieldName, state] of this.fieldStates) {
            if (state.isValid === true) {
                stats.validFields++;
            } else if (state.isValid === false) {
                stats.invalidFields++;
            } else {
                stats.unvalidatedFields++;
            }
        }
        
        return stats;
    }
    
    /**
     * 销毁监听器
     */
    destroy() {
        // 清理防抖定时器
        for (const timer of this.debounceTimers.values()) {
            clearTimeout(timer);
        }
        this.debounceTimers.clear();
        
        // 重置验证状态
        this.resetValidation();
        
        // 标记为未初始化
        this.isInitialized = false;
        
        console.log('[FormValidationListener] 监听器已销毁');
    }
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.FormValidationListener = FormValidationListener;
}

export default FormValidationListener;
```

## 3. 集成步骤

### 3.1 在主应用中初始化

在 `js/managers/listener-manager.js` 中创建监听器管理器：

```javascript
/**
 * 监听器管理器 - 统一管理所有监听器
 * @MANAGER @LISTENER_COORDINATOR
 */
import ListenerRegistry from '../listeners/core/listener-registry.js';
import ListenerErrorHandler from '../listeners/core/error-handler.js';
import FormValidationListener from '../listeners/form/form-validation-listener.js';
// 其他监听器导入...

class ListenerManager {
    constructor() {
        this.registry = null;
        this.errorHandler = null;
        this.isInitialized = false;
        
        console.log('[ListenerManager] 监听器管理器已创建');
    }
    
    /**
     * 初始化所有监听器
     */
    async initialize() {
        if (this.isInitialized) {
            console.warn('[ListenerManager] 监听器管理器已经初始化');
            return;
        }
        
        try {
            // 1. 创建注册中心
            this.registry = new ListenerRegistry();
            
            // 2. 创建错误处理器
            this.errorHandler = new ListenerErrorHandler();
            this.registry.setErrorHandler(this.errorHandler);
            
            // 3. 注册核心监听器
            await this.registerCoreListeners();
            
            // 4. 注册业务监听器
            await this.registerBusinessListeners();
            
            // 5. 设置全局引用
            this.setupGlobalReferences();
            
            this.isInitialized = true;
            console.log('[ListenerManager] 所有监听器初始化完成');
            
        } catch (error) {
            console.error('[ListenerManager] 初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 注册核心监听器
     */
    async registerCoreListeners() {
        // 表单验证监听器
        const formManager = window.OTA?.managers?.form;
        if (formManager) {
            const formValidationListener = new FormValidationListener(formManager);
            this.registry.register('formValidation', formValidationListener, 90);
        }
        
        // 其他核心监听器...
    }
    
    /**
     * 注册业务监听器
     */
    async registerBusinessListeners() {
        // 根据项目需要注册业务相关的监听器
    }
    
    /**
     * 设置全局引用
     */
    setupGlobalReferences() {
        if (typeof window !== 'undefined') {
            window.OTA = window.OTA || {};
            window.OTA.listenerManager = this;
        }
    }
    
    /**
     * 获取监听器
     * @param {string} name - 监听器名称
     */
    getListener(name) {
        return this.registry ? this.registry.get(name) : null;
    }
    
    /**
     * 分发事件
     * @param {string} eventType - 事件类型
     * @param {Object} eventData - 事件数据
     */
    dispatchEvent(eventType, eventData) {
        if (this.registry) {
            this.registry.dispatchEvent(eventType, eventData);
        }
    }
    
    /**
     * 获取统计信息
     */
    getStats() {
        return {
            registry: this.registry ? this.registry.getStats() : null,
            errors: this.errorHandler ? this.errorHandler.getErrorStats() : null
        };
    }
    
    /**
     * 销毁管理器
     */
    destroy() {
        if (this.registry) {
            this.registry.destroy();
        }
        
        if (this.errorHandler) {
            this.errorHandler.destroy();
        }
        
        this.isInitialized = false;
        
        console.log('[ListenerManager] 监听器管理器已销毁');
    }
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.ListenerManager = ListenerManager;
}

export default ListenerManager;
```

### 3.2 在应用启动时初始化

在主应用文件中添加初始化代码：

```javascript
// 在现有的初始化代码中添加
import ListenerManager from './js/managers/listener-manager.js';

// 应用初始化函数
async function initializeApp() {
    try {
        // 现有的初始化代码...
        
        // 初始化监听器管理器
        const listenerManager = new ListenerManager();
        await listenerManager.initialize();
        
        console.log('✅ 应用初始化完成');
        
    } catch (error) {
        console.error('❌ 应用初始化失败:', error);
    }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}
```

## 4. 测试和验证

### 4.1 创建测试工具

创建 `js/listeners/utils/test-helper.js`：

```javascript
/**
 * 监听器测试辅助工具
 * @TEST @HELPER
 */
class ListenerTestHelper {
    constructor() {
        this.testResults = new Map();
        this.mockEvents = [];
    }
    
    /**
     * 测试监听器响应
     * @param {string} listenerName - 监听器名称
     * @param {string} eventType - 事件类型
     * @param {Object} testData - 测试数据
     */
    async testListener(listenerName, eventType, testData) {
        const startTime = performance.now();
        
        try {
            // 获取监听器
            const listener = window.OTA?.listenerRegistry?.get(listenerName);
            if (!listener) {
                throw new Error(`监听器 "${listenerName}" 不存在`);
            }
            
            // 触发事件
            const event = new CustomEvent(eventType, { detail: testData });
            document.dispatchEvent(event);
            
            // 等待处理完成
            await this.waitForProcessing(100);
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            this.testResults.set(`${listenerName}_${eventType}`, {
                success: true,
                duration,
                timestamp: Date.now()
            });
            
            console.log(`✅ 测试通过: ${listenerName} - ${eventType} (${duration.toFixed(2)}ms)`);
            return true;
            
        } catch (error) {
            this.testResults.set(`${listenerName}_${eventType}`, {
                success: false,
                error: error.message,
                timestamp: Date.now()
            });
            
            console.error(`❌ 测试失败: ${listenerName} - ${eventType}:`, error);
            return false;
        }
    }
    
    /**
     * 等待处理完成
     * @param {number} timeout - 超时时间（毫秒）
     */
    waitForProcessing(timeout = 1000) {
        return new Promise((resolve) => {
            setTimeout(resolve, timeout);
        });
    }
    
    /**
     * 运行所有测试
     */
    async runAllTests() {
        const tests = [
            // 表单验证测试
            {
                listener: 'formValidation',
                event: 'input',
                data: { fieldName: 'customerName', value: '张三' }
            },
            {
                listener: 'formValidation',
                event: 'blur',
                data: { fieldName: 'contactNumber', value: '13800138000' }
            },
            // 更多测试用例...
        ];
        
        console.log(`开始运行 ${tests.length} 个测试...`);
        
        for (const test of tests) {
            await this.testListener(test.listener, test.event, test.data);
        }
        
        this.generateReport();
    }
    
    /**
     * 生成测试报告
     */
    generateReport() {
        const report = {
            totalTests: this.testResults.size,
            passed: 0,
            failed: 0,
            details: []
        };
        
        for (const [testName, result] of this.testResults) {
            if (result.success) {
                report.passed++;
            } else {
                report.failed++;
            }
            
            report.details.push({
                test: testName,
                status: result.success ? 'PASSED' : 'FAILED',
                duration: result.duration || 0,
                error: result.error || null
            });
        }
        
        console.log('\n=== 监听器测试报告 ===');
        console.log(`总测试数: ${report.totalTests}`);
        console.log(`通过: ${report.passed}`);
        console.log(`失败: ${report.failed}`);
        console.log(`成功率: ${((report.passed / report.totalTests) * 100).toFixed(2)}%`);
        
        if (report.failed > 0) {
            console.log('\n失败的测试:');
            report.details
                .filter(detail => detail.status === 'FAILED')
                .forEach(detail => {
                    console.log(`- ${detail.test}: ${detail.error}`);
                });
        }
        
        return report;
    }
    
    /**
     * 清理测试结果
     */
    clearResults() {
        this.testResults.clear();
        this.mockEvents = [];
        console.log('[ListenerTestHelper] 测试结果已清理');
    }
}

// 导出到全局
if (typeof window !== 'undefined') {
    window.ListenerTestHelper = ListenerTestHelper;
}

export default ListenerTestHelper;
```

### 4.2 在浏览器控制台中测试

```javascript
// 在浏览器控制台中运行以下代码进行测试

// 1. 创建测试助手
const testHelper = new ListenerTestHelper();

// 2. 运行所有测试
testHelper.runAllTests();

// 3. 查看监听器状态
console.log('监听器统计:', window.OTA.listenerManager.getStats());

// 4. 手动测试特定监听器
testHelper.testListener('formValidation', 'input', {
    fieldName: 'customerName',
    value: '测试用户'
});
```

## 5. 性能优化建议

### 5.1 防抖和节流

创建 `js/listeners/utils/debounce.js`：

```javascript
/**
 * 防抖工具函数
 * @UTIL @PERFORMANCE
 */
export function debounce(func, wait, immediate = false) {
    let timeout;
    
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func.apply(this, args);
        };
        
        const callNow = immediate && !timeout;
        
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        
        if (callNow) func.apply(this, args);
    };
}

/**
 * 节流工具函数
 * @UTIL @PERFORMANCE
 */
export function throttle(func, limit) {
    let inThrottle;
    
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 智能防抖（根据输入频率自动调整延迟）
 * @UTIL @PERFORMANCE
 */
export function smartDebounce(func, minWait = 100, maxWait = 1000) {
    let timeout;
    let lastCallTime = 0;
    let callCount = 0;
    
    return function executedFunction(...args) {
        const now = Date.now();
        const timeSinceLastCall = now - lastCallTime;
        
        // 计算动态延迟
        callCount++;
        const dynamicWait = Math.min(
            minWait + (callCount * 50), // 随调用次数增加延迟
            maxWait
        );
        
        // 如果调用间隔很长，重置计数
        if (timeSinceLastCall > maxWait) {
            callCount = 1;
        }
        
        lastCallTime = now;
        
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            callCount = 0; // 执行后重置计数
            func.apply(this, args);
        }, dynamicWait);
    };
}
```

### 5.2 内存管理

在监听器中添加内存清理机制：

```javascript
// 在每个监听器类中添加
class SomeListener {
    constructor() {
        this.eventListeners = new Map(); // 跟踪所有事件监听器
        this.timers = new Set(); // 跟踪所有定时器
        this.observers = new Set(); // 跟踪所有观察者
    }
    
    // 添加事件监听器时记录
    addEventListener(element, event, handler, options) {
        element.addEventListener(event, handler, options);
        
        const key = `${element.tagName}_${event}`;
        if (!this.eventListeners.has(key)) {
            this.eventListeners.set(key, []);
        }
        this.eventListeners.get(key).push({ element, event, handler });
    }
    
    // 设置定时器时记录
    setTimeout(callback, delay) {
        const timerId = setTimeout(() => {
            this.timers.delete(timerId);
            callback();
        }, delay);
        
        this.timers.add(timerId);
        return timerId;
    }
    
    // 销毁时清理所有资源
    destroy() {
        // 清理事件监听器
        for (const listeners of this.eventListeners.values()) {
            listeners.forEach(({ element, event, handler }) => {
                element.removeEventListener(event, handler);
            });
        }
        this.eventListeners.clear();
        
        // 清理定时器
        this.timers.forEach(timerId => clearTimeout(timerId));
        this.timers.clear();
        
        // 清理观察者
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
    }
}
```

## 6. 故障排除

### 6.1 常见问题

1. **监听器未响应**
   - 检查监听器是否正确注册
   - 验证事件名称是否正确
   - 确认元素是否存在

2. **性能问题**
   - 检查是否有过多的事件监听器
   - 验证防抖/节流是否正确配置
   - 监控内存使用情况

3. **错误处理**
   - 查看错误处理器的日志
   - 检查监听器的错误计数
   - 验证错误恢复机制

### 6.2 调试工具

```javascript
// 在浏览器控制台中使用的调试命令

// 查看所有监听器状态
window.OTA.listenerRegistry.getAll();

// 查看错误统计
window.OTA.listenerManager.errorHandler.getErrorStats();

// 手动触发事件
window.OTA.listenerRegistry.dispatchEvent('testEvent', { test: true });

// 重新激活监听器
window.OTA.listenerRegistry.reactivate('formValidation');
```

## 7. 部署清单

### 7.1 部署前检查

- [ ] 所有监听器已正确注册
- [ ] 错误处理机制已配置
- [ ] 性能优化已实施
- [ ] 测试已通过
- [ ] 文档已更新

### 7.2 监控指标

- 监听器响应时间
- 错误发生率
- 内存使用情况
- 用户交互响应性

### 7.3 回滚计划

如果出现问题，可以通过以下方式快速回滚：

```javascript
// 禁用所有新监听器
window.OTA.listenerManager.destroy();

// 恢复到原有的事件处理机制
// （保留原有代码作为备份）
```

---

通过以上实施指南，您可以系统地为项目添加完整的监听器操作功能，提升用户体验和系统稳定性。