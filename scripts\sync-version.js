#!/usr/bin/env node
/**
 * 版本同步脚本 - 统一版本管理系统
 * 
 * 功能：
 * 1. 从 version.json 读取统一版本配置
 * 2. 自动同步版本号到所有目标文件
 * 3. 更新构建信息和时间戳
 * 4. 生成版本更新报告
 * 
 * 使用方法：
 * - npm run version:sync          // 同步当前版本
 * - npm run version:update 2.3.0  // 更新并同步新版本
 */

const fs = require('fs');
const path = require('path');

class VersionManager {
    constructor() {
        this.configPath = path.join(__dirname, '..', 'version.json');
        this.config = this.loadConfig();
        this.updated = [];
        this.errors = [];
    }

    loadConfig() {
        try {
            const content = fs.readFileSync(this.configPath, 'utf8');
            return JSON.parse(content);
        } catch (error) {
            console.error('❌ 无法读取版本配置文件:', error.message);
            process.exit(1);
        }
    }

    saveConfig() {
        try {
            this.config.lastUpdated = new Date().toISOString().split('T')[0];
            this.config.buildInfo.lastBuild = new Date().toISOString();
            fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
            console.log('✅ 版本配置已更新');
        } catch (error) {
            this.errors.push(`保存配置失败: ${error.message}`);
        }
    }

    updateVersion(newVersion) {
        if (newVersion) {
            console.log(`🔄 更新版本号: ${this.config.version} → ${newVersion}`);
            this.config.version = newVersion;
            this.saveConfig();
        }
    }

    syncPackageJson() {
        try {
            const filePath = 'package.json';
            const content = fs.readFileSync(filePath, 'utf8');
            const packageJson = JSON.parse(content);
            
            packageJson.version = this.config.version;
            
            fs.writeFileSync(filePath, JSON.stringify(packageJson, null, 2));
            this.updated.push(`package.json → ${this.config.version}`);
        } catch (error) {
            this.errors.push(`package.json: ${error.message}`);
        }
    }

    syncManifestJson() {
        try {
            const filePath = 'manifest.json';
            const content = fs.readFileSync(filePath, 'utf8');
            const manifest = JSON.parse(content);
            
            manifest.version = this.config.version;
            
            fs.writeFileSync(filePath, JSON.stringify(manifest, null, 2));
            this.updated.push(`manifest.json → ${this.config.version}`);
        } catch (error) {
            this.errors.push(`manifest.json: ${error.message}`);
        }
    }

    syncScriptManifest() {
        try {
            const filePath = 'js/core/script-manifest.js';
            let content = fs.readFileSync(filePath, 'utf8');
            
            const versionRegex = /version:\s*['"`]([^'"`\n]+)['"`]/;
            content = content.replace(versionRegex, `version: '${this.config.version}'`);
            
            fs.writeFileSync(filePath, content);
            this.updated.push(`script-manifest.js → ${this.config.version}`);
        } catch (error) {
            this.errors.push(`script-manifest.js: ${error.message}`);
        }
    }

    syncMainJs() {
        try {
            const filePath = 'main.js';
            let content = fs.readFileSync(filePath, 'utf8');
            
            const versionRegex = /@version\s+[\d.]+/;
            content = content.replace(versionRegex, `@version ${this.config.version}`);
            
            const dateRegex = /@lastModified\s+[\d-]+/;
            content = content.replace(dateRegex, `@lastModified ${this.config.lastUpdated}`);
            
            fs.writeFileSync(filePath, content);
            this.updated.push(`main.js → ${this.config.version}`);
        } catch (error) {
            this.errors.push(`main.js: ${error.message}`);
        }
    }

    syncOrderHistoryManager() {
        try {
            const filePath = 'js/order-history-manager.js';
            let content = fs.readFileSync(filePath, 'utf8');
            
            const versionRegex = /@version\s+[\d.]+/;
            content = content.replace(versionRegex, `@version ${this.config.version}`);
            
            fs.writeFileSync(filePath, content);
            this.updated.push(`order-history-manager.js → ${this.config.version}`);
        } catch (error) {
            this.errors.push(`order-history-manager.js: ${error.message}`);
        }
    }

    syncI18nJs() {
        try {
            const filePath = 'js/i18n.js';
            let content = fs.readFileSync(filePath, 'utf8');
            
            const versionRegex = /@version\s+[\d.]+/;
            content = content.replace(versionRegex, `@version ${this.config.version}`);
            
            fs.writeFileSync(filePath, content);
            this.updated.push(`i18n.js → ${this.config.version}`);
        } catch (error) {
            this.errors.push(`i18n.js: ${error.message}`);
        }
    }

    syncProductionConfig() {
        try {
            const filePath = 'production-config.js';
            let content = fs.readFileSync(filePath, 'utf8');
            
            // 更新 app.version
            const appVersionRegex = /(version:\s*['"`])[\d.-]+(-linus-refactor['"`])/;
            content = content.replace(appVersionRegex, `$1${this.config.version}$2`);
            
            // 更新 cache.version
            const cacheVersionRegex = /(version:\s*['"`]v)[\d.]+(['"`])/;
            content = content.replace(cacheVersionRegex, `$1${this.config.version}$2`);
            
            // 更新 cdn.version
            const cdnVersionRegex = /(version:\s*['"`])[\d.]+(['"`])/;
            content = content.replace(cdnVersionRegex, `$1${this.config.version}$2`);
            
            fs.writeFileSync(filePath, content);
            this.updated.push(`production-config.js → ${this.config.version}`);
        } catch (error) {
            this.errors.push(`production-config.js: ${error.message}`);
        }
    }

    syncServiceWorker() {
        try {
            const filePath = 'sw.js';
            let content = fs.readFileSync(filePath, 'utf8');
            
            const cacheNameRegex = /(CACHE_NAME\s*=\s*['"`]ota-linus-v)[\d.]+(-silent['"`])/;
            content = content.replace(cacheNameRegex, `$1${this.config.version}$2`);
            
            fs.writeFileSync(filePath, content);
            this.updated.push(`sw.js → ${this.config.version}`);
        } catch (error) {
            this.errors.push(`sw.js: ${error.message}`);
        }
    }

    syncAll() {
        console.log(`🚀 开始同步版本号: ${this.config.version}`);
        console.log('📁 目标文件:', this.config.targets.length, '个');
        
        this.syncPackageJson();
        this.syncManifestJson();
        this.syncScriptManifest();
        this.syncMainJs();
        this.syncOrderHistoryManager();
        this.syncI18nJs();
        this.syncProductionConfig();
        this.syncServiceWorker();
    }

    generateReport() {
        console.log('\n📊 版本同步报告');
        console.log('================');
        console.log(`📌 当前版本: ${this.config.version}`);
        console.log(`📅 更新时间: ${this.config.lastUpdated}`);
        console.log(`✅ 成功更新: ${this.updated.length} 个文件`);
        
        if (this.updated.length > 0) {
            console.log('\n成功更新的文件:');
            this.updated.forEach(item => console.log(`  ✓ ${item}`));
        }
        
        if (this.errors.length > 0) {
            console.log(`\n❌ 失败: ${this.errors.length} 个文件`);
            this.errors.forEach(error => console.log(`  ✗ ${error}`));
        }
        
        console.log('\n🔧 下一步: 运行 npm run build 生成构建信息');
    }

    run() {
        const newVersion = process.argv[2];
        
        if (newVersion) {
            this.updateVersion(newVersion);
        }
        
        this.syncAll();
        this.generateReport();
        
        if (this.errors.length === 0) {
            console.log('\n🎉 版本同步完成！');
            process.exit(0);
        } else {
            console.log('\n⚠️ 部分文件同步失败，请检查错误信息');
            process.exit(1);
        }
    }
}

// 运行版本管理器
const versionManager = new VersionManager();
versionManager.run();
