# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个OTA订单处理系统，采用静态单页应用(SPA)架构，专为GoMyHire集成设计。系统使用原生JavaScript实现，无打包工具，通过依赖注入(DI)容器和服务定位器模式管理模块依赖。

## 核心架构

### 依赖注入系统
- **依赖容器**: `js/core/dependency-container.js` - 管理服务注册和实例化
- **服务定位器**: `js/core/service-locator.js` - 统一服务获取接口，支持降级方案
- **应用启动器**: `js/core/application-bootstrap.js` - 5阶段启动流程管理

### 脚本加载架构
- **脚本清单**: `js/core/script-manifest.js` - 定义5阶段加载顺序
- **脚本加载器**: `js/core/script-loader.js` - 按阶段加载脚本
- **阶段划分**:
  1. infrastructure: 纯基础设施
  2. configuration: 配置和类定义
  3. services: 服务实现层
  4. managers: 管理器实例化
  5. launch: 验证和启动

### 母子两层架构
- **母层控制器**: 业务流程控制
- **子层实现**: Flow层(渠道检测、提示词构建、Gemini调用) + Order层(订单处理、API调用)

## 常用开发命令

### 构建和部署
```bash
npm run build                    # 构建项目（验证部署 + 生成构建信息）
npm run version:sync             # 同步版本号到所有文件
npm run version:update 2.4.3     # 更新并同步新版本
npm run validate                 # 验证部署配置
```

### 版本管理
- 版本配置文件: `version.json` (统一版本数据源)
- 版本同步脚本: `scripts/sync-version.js`
- 支持文件: package.json, manifest.json, script-manifest.js等

### 开发和测试
```bash
npm start                        # 启动开发服务器（静态文件）
npm test                         # 运行测试
```

## 关键文件结构

### 核心系统
```
js/core/
├── dependency-container.js      # 依赖注入容器
├── service-locator.js          # 服务定位器
├── application-bootstrap.js     # 启动协调器
├── script-manifest.js          # 脚本加载清单
├── script-loader.js            # 脚本加载器
└── global-event-coordinator.js  # 全局事件协调器
```

### 管理器系统
```
js/managers/
├── form-manager.js             # 表单管理器
├── event-manager.js            # 事件管理器
├── ui-state-manager.js         # UI状态管理器
├── permission-manager.js       # 权限管理器
├── realtime-analysis-manager.js # 实时分析管理器
└── animation-manager.js        # 动画管理器
```

### 业务流程
```
js/flow/           # Flow层实现
├── channel-detector.js
├── prompt-builder.js
├── gemini-caller.js
└── result-processor.js

js/order/          # Order层实现
├── multi-order-handler.js
├── api-caller.js
└── history-manager.js

js/controllers/    # 控制器层
├── business-flow-controller.js
└── order-management-controller.js
```

### 多订单系统
```
js/multi-order/
├── multi-order-detector.js     # 多订单检测
├── multi-order-processor.js    # 多订单处理
├── multi-order-renderer.js     # 多订单渲染
├── multi-order-state-manager.js # 状态管理
└── batch-processor.js          # 批量处理
```

## 重要的开发约定

### 服务注册和使用
```javascript
// 注册服务
window.OTA.registerService('serviceName', () => ({
    init() {}
}));

// 使用服务
const service = window.OTA.getService('serviceName');
// 或使用便捷函数
const logger = getLogger();
const appState = getAppState();
```

### 脚本加载顺序
严格遵循script-manifest.js中定义的5阶段加载顺序，确保依赖关系正确。

### DOM元素约定
主要DOM元素ID（index.html中定义）:
- `loginPanel` - 登录面板
- `workspace` - 主工作区
- `orderForm` - 订单表单
- `orderInput` - 订单输入框
- `ota` - OTA渠道选择
- `carTypeId` - 车型选择
- `subCategoryId` - 服务类型选择
- `drivingRegionId` - 行驶区域选择

### 字段命名约定
- 表单字段使用 snake_case (如: customer_name, pickup_date)
- JavaScript变量使用 camelCase
- API字段使用 snake_case

## 调试和诊断

### 启动调试
```javascript
// 获取启动报告
window.OTA.debug.getStartupReport();

// 重启应用
window.OTA.debug.restart();

// 检查服务状态
window.OTA.container.getRegisteredServices();
window.OTA.serviceLocator.getAvailableServices();
```

### 常见调试命令
```javascript
// 检查FormManager状态
window.OTA.getService('formManager');

// 检查多订单系统
window.OTA.getService('multiOrderManager');

// 查看应用状态
getAppState().getAll();
```

## 部署配置

### Netlify配置
- 配置文件: `netlify.toml`
- 发布目录: 当前目录(".")
- 函数目录: `netlify/functions`
- Node版本: 18

### 环境变量
在Netlify dashboard中设置:
- `FLIGHTAWARE_API_KEY` - FlightAware API密钥
- 其他API密钥和配置

### 安全策略
CSP配置允许的域名:
- `gomyhire.com.my`
- `generativelanguage.googleapis.com`
- `aeroapi.flightaware.com`

## 测试

### 测试文件位置
- 根目录: test-*.html (各种功能测试)
- tests/ 目录: 综合测试页面
- archive/tests/: 历史测试文件

### 手动测试
- 在浏览器中直接打开HTML测试文件
- 使用浏览器开发者工具调试
- 查看控制台日志和错误信息

## 性能优化

### 加载优化
- 5阶段渐进式加载
- 按需加载大型数据文件
- 延迟初始化非关键服务

### 缓存策略
- CSS文件: 长期缓存(1年)
- JS文件: 版本化缓存(1小时)
- HTML文件: 不缓存

## 兼容性

### 浏览器支持
- 现代浏览器(Chrome, Firefox, Safari, Edge)
- 移动端浏览器
- 降级方案处理旧浏览器

### API兼容性
- 保持向后兼容的服务获取方式
- 渐进式增强功能
- 优雅降级处理错误

## 重要提醒

1. **严格遵循脚本加载顺序** - 修改script-manifest.js时需谨慎
2. **使用服务定位器** - 避免直接访问全局变量
3. **保持字段命名一致** - 表单字段使用snake_case
4. **版本同步** - 修改代码后运行version:sync
5. **测试验证** - 部署前运行validate命令