# 统一版本管理系统

## 概述

为了解决版本号分散在多个文件中造成的维护困扰，我们引入了统一版本管理系统。现在您只需要在一个地方管理版本号！

## 核心文件

### 📄 `version.json` - 版本配置中心
```json
{
  "version": "2.2.0",
  "name": "OTA订单处理系统",
  "description": "统一版本配置文件 - 所有版本号的单一数据源",
  "lastUpdated": "2025-08-19",
  "targets": [
    "package.json",
    "manifest.json", 
    "js/core/script-manifest.js",
    "main.js",
    "js/order-history-manager.js",
    "js/i18n.js",
    "production-config.js",
    "sw.js"
  ]
}
```

### 🔧 `scripts/sync-version.js` - 自动同步脚本
智能版本同步工具，自动更新所有目标文件中的版本号。

## 使用方法

### 1. 📋 同步当前版本到所有文件
```bash
npm run version:sync
```

### 2. 🚀 更新版本号并同步
```bash
npm run version:update 2.3.0
```

### 3. 🔨 版本同步 + 构建
```bash
npm run version:build
```

## 管理的文件

系统会自动同步版本号到以下文件：

| 文件 | 更新内容 | 说明 |
|------|----------|------|
| `package.json` | `version` 字段 | NPM 包版本 |
| `manifest.json` | `version` 字段 | PWA 应用版本 |
| `js/core/script-manifest.js` | `version` 属性 | 脚本清单版本 |
| `main.js` | `@version` 注释 | 主脚本版本注释 |
| `js/order-history-manager.js` | `@version` 注释 | 模块版本注释 |
| `js/i18n.js` | `@version` 注释 | 模块版本注释 |
| `production-config.js` | 多个版本字段 | 生产配置版本 |
| `sw.js` | `CACHE_NAME` | Service Worker 缓存版本 |

## 工作流程

### 🔄 版本更新流程
1. 修改 `version.json` 中的版本号（或使用命令行参数）
2. 运行同步脚本
3. 脚本自动更新所有目标文件
4. 生成详细的更新报告
5. 可选：运行构建过程

### 📊 同步报告示例
```
🚀 开始同步版本号: 2.3.0
📁 目标文件: 8 个

📊 版本同步报告
================
📌 当前版本: 2.3.0
📅 更新时间: 2025-08-19
✅ 成功更新: 8 个文件

成功更新的文件:
  ✓ package.json → 2.3.0
  ✓ manifest.json → 2.3.0
  ✓ script-manifest.js → 2.3.0
  ✓ main.js → 2.3.0
  ✓ order-history-manager.js → 2.3.0
  ✓ i18n.js → 2.3.0
  ✓ production-config.js → 2.3.0
  ✓ sw.js → 2.3.0

🔧 下一步: 运行 npm run build 生成构建信息
```

## 优势

### ✅ 解决的问题
- ❌ **旧方式**: 版本号分散在 8+ 个文件中，需要手动逐一更新
- ✅ **新方式**: 统一管理，一个命令更新所有文件

### 🎯 主要优势
1. **单一数据源**: 版本号只在 `version.json` 中定义
2. **自动化**: 一个命令同步所有文件
3. **报告机制**: 详细的成功/失败报告
4. **向后兼容**: 构建脚本支持降级读取
5. **错误处理**: 智能错误处理和回滚机制

## 扩展

### 添加新的目标文件
1. 在 `version.json` 的 `targets` 数组中添加文件路径
2. 在 `scripts/sync-version.js` 中添加对应的同步方法
3. 运行测试确保同步正常

### 自定义版本格式
可以在 `version.json` 中添加更多版本相关的元数据，脚本会自动处理。

## 故障排除

### 常见问题
1. **权限错误**: 确保脚本有文件写入权限
2. **文件不存在**: 检查 `targets` 中的文件路径是否正确
3. **格式错误**: 确保 `version.json` 是有效的 JSON 格式

### 恢复机制
如果同步失败，您可以：
1. 检查错误报告找出问题文件
2. 手动修复问题文件
3. 重新运行同步脚本

---

🎉 **享受统一版本管理的便利！再也不用为版本号分散在各处而烦恼了！**
