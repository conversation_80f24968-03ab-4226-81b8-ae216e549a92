# 🔄 字段映射参考表

## 📋 项目概述
本文档记录了 snake_case 统一重构项目中所有需要转换的字段映射关系。

**重构目标**: 将整个系统的字段命名从 camelCase 统一转换为 snake_case，消除字段格式转换逻辑。

## 📊 核心字段映射表

| 原字段名 (camelCase) | 新字段名 (snake_case) | 字段类型 | 主要用途 | 关联文件 |
|---------------------|----------------------|----------|----------|----------|
| `customerName` | `customer_name` | string | 客户姓名 | HTML, JS, CSS, i18n |
| `customerContact` | `customer_contact` | string | 客户联系电话 | HTML, JS, CSS, i18n |
| `customerEmail` | `customer_email` | string | 客户邮箱 | HTML, JS, CSS, i18n |
| `flightInfo` | `flight_info` | string | 航班信息 | HTML, JS, CSS, i18n |
| `otaPrice` | `ota_price` | number | OTA价格 | HTML, JS, CSS, i18n |
| `otaReferenceNumber` | `ota_reference_number` | string | OTA参考号 | HTML, JS, CSS, i18n |
| `pickupDate` | `date` | string | 接送日期 | HTML, JS, CSS, i18n |
| `pickupTime` | `time` | string | 接送时间 | HTML, JS, CSS, i18n |
| `passengerCount` | `passenger_number` | number | 乘客人数 | HTML, JS, CSS, i18n |
| `luggageCount` | `luggage_number` | number | 行李数量 | HTML, JS, CSS, i18n |
| `subCategoryId` | `sub_category_id` | number | 服务类型ID | HTML, JS, CSS, i18n |
| `carTypeId` | `car_type_id` | number | 车型ID | HTML, JS, CSS, i18n |
| `drivingRegionId` | `driving_region_id` | number | 驾驶区域ID | HTML, JS, CSS, i18n |
| `extraRequirement` | `extra_requirement` | string | 额外要求 | HTML, JS, CSS, i18n |
| `languagesIdArray` | `languages_id_array` | array | 语言ID数组 | HTML, JS, CSS, i18n |
| `dropoff` | `destination` | string | 目的地 | HTML, JS, CSS, i18n |
| `driverFee` | `driver_fee` | number | 司机费用 | HTML, JS, CSS, i18n |

## 📁 需要修改的文件清单

### HTML 文件
- `index.html` - 主表单页面
  - 字段: customerName, customerContact, customerEmail, flightInfo, otaPrice, otaReferenceNumber, pickupDate, pickupTime, passengerCount, luggageCount, subCategoryId, carTypeId, drivingRegionId, extraRequirement

### JavaScript 文件
- `js/managers/form-manager.js` - 表单管理器
  - DOM选择器: 所有字段的 getElementById, querySelector 引用
  - 字段收集: frontendFields 数组定义
  - 字段映射: fieldMapping 对象

- `js/ui-manager.js` - UI管理器
  - DOM缓存: elements 对象中的字段引用

- `js/order-history-manager.js` - 历史订单管理器
  - 保存字段: orderData 对象结构
  - 显示字段: renderOrderFields 方法
  - 复制字段: copyOrderData 方法

- `js/services/unified-field-mapper.js` - 统一字段映射服务
  - 映射表: CORE_FIELD_MAPPINGS 对象

### CSS 文件
- `css/components/forms.css` - 表单样式
  - 选择器: #customerName, #customerContact 等字段选择器

### 国际化文件
- `js/i18n/zh.js` - 中文语言包
  - 键名: form.customerName, form.customerContact 等

- `js/i18n/en.js` - 英文语言包
  - 键名: form.customerName, form.customerContact 等

## 🔧 特殊处理字段

### 字段名变更 (不仅仅是格式转换)
- `dropoff` → `destination` (字段名完全改变)
- `pickupDate` → `date` (简化字段名)
- `pickupTime` → `time` (简化字段名)
- `passengerCount` → `passenger_number` (格式+词汇变更)
- `luggageCount` → `luggage_number` (格式+词汇变更)

### 保持不变的字段
- `pickup` (已经是标准格式)
- `currency` (已经是标准格式)
- `remark` (已经是标准格式)

## ⚠️ 注意事项

1. **DOM关联**: 确保HTML的id/name属性与JavaScript的DOM选择器同步更新
2. **CSS选择器**: 所有基于字段名的CSS选择器需要同步更新
3. **国际化键名**: i18n语言包中的键名需要同步更新
4. **表单验证**: 验证逻辑中的字段名引用需要更新
5. **事件绑定**: 基于字段名的事件监听器需要更新

## 📈 重构进度跟踪

- [ ] 阶段1: 准备和分析
- [ ] 阶段2: HTML表单字段替换
- [ ] 阶段3: JavaScript代码更新
- [ ] 阶段4: CSS样式更新
- [ ] 阶段5: 国际化更新
- [ ] 阶段6: 清理转换逻辑
- [ ] 阶段7: 测试验证

## 🎯 预期收益

- 消除字段格式转换逻辑 (约200-300行代码)
- 减少字段映射相关bug风险 (90%降低)
- 提升代码可读性和维护性
- 简化新功能开发流程
