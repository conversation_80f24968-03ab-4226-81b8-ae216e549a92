flowchart TD
    %% 页面入口和基础架构
    A["index.html + 页面结构"] --> B["script-manifest.js + 5阶段加载架构"]
    B --> C["script-loader.js + 脚本加载器"]
    
    %% 阶段1: 基础设施
    C --> D1["dependency-container.js + 依赖注入容器"]
    C --> D2["service-locator.js + 服务定位器"]
    C --> D3["global-event-coordinator.js + 全局事件协调"]
    
    %% 阶段2: 配置和类定义
    D1 --> E1["vehicle-configuration-manager.js + 车辆配置"]
    D2 --> E2["form-manager.js + 表单管理器类定义"]
    D3 --> E3["language-detector.js + 语言检测"]
    
    %% 阶段3: 服务实现和业务逻辑
    E1 --> F1["business-flow-controller.js + 核心业务流程控制器"]
    E2 --> F2["channel-detector.js + 渠道检测子层"]
    E3 --> F3["prompt-builder.js + 提示词构建子层"]
    F1 --> F4["gemini-caller.js + Gemini API调用子层"]
    F1 --> F5["result-processor.js + 结果处理子层"]
    
    %% 阶段4: 管理器实例化
    F1 --> G1["form-manager.js + 表单管理器实例"]
    F2 --> G2["event-manager.js + 事件管理器"]
    F3 --> G3["permission-manager.js + 权限管理器"]
    F4 --> G4["ui-state-manager.js + UI状态管理器"]
    F5 --> G5["animation-manager.js + 动画管理器"]
    G1 --> G6["realtime-analysis-manager.js + 实时分析管理器"]
    
    %% 阶段5: 启动和主界面
    G1 --> H1["application-bootstrap.js + 应用启动器"]
    G2 --> H2["main.js + 主入口脚本"]
    
    %% 核心服务层
    H1 --> I1["unified-field-mapper.js + 统一字段映射"]
    H2 --> I2["component-lifecycle-manager.js + 组件生命周期"]
    I1 --> I3["feature-toggle.js + 功能开关"]
    I2 --> I4["vehicle-config-integration.js + 车辆配置集成"]
    
    %% 业务流程详细展开
    F1 --> J1["输入处理 + 文字/图片"]
    J1 --> J2["渠道检测 + 本地特征识别"]
    J2 --> J3["提示词构建 + 渠道专属组合"]
    J3 --> J4["Gemini API调用 + 智能解析"]
    J4 --> J5["结果处理 + 单/多订单分支"]
    J5 --> J6["订单管理 + 后续处理"]
    
    %% 数据流和状态管理
    G1 --> K1["表单数据收集 + 验证处理"]
    G4 --> K2["UI状态同步 + 界面更新"]
    G6 --> K3["实时分析 + 自动触发"]
    I1 --> K4["数据格式转换 + camelCase/snake_case"]
    
    %% 权限和配置
    G3 --> L1["价格字段权限 + 显示控制"]
    G3 --> L2["语言选项权限 + 功能限制"]
    I3 --> L3["功能开关 + 动态配置"]
    I4 --> L4["车辆配置 + 动态加载"]
    
    %% 事件和动画
    G2 --> M1["全局事件处理 + 跨组件通信"]
    G5 --> M2["UI动画效果 + 用户体验"]
    
    %% 样式和资源
    A --> N1["css/main.css + 样式表"]
    
    %% 依赖关系说明
    classDef entryPoint fill:#e1f5fe
    classDef coreInfra fill:#f3e5f5
    classDef businessLogic fill:#e8f5e8
    classDef managers fill:#fff3e0
    classDef services fill:#fce4ec
    classDef ui fill:#f1f8e9
    
    class A entryPoint
    class B,C,D1,D2,D3 coreInfra
    class F1,F2,F3,F4,F5,J1,J2,J3,J4,J5,J6 businessLogic
    class G1,G2,G3,G4,G5,G6 managers
    class I1,I2,I3,I4,K4 services
    class K1,K2,K3,L1,L2,L3,L4,M1,M2,N1 ui